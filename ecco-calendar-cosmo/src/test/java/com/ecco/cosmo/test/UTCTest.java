package com.ecco.cosmo.test;

import java.text.ParseException;
import java.util.Calendar;
import java.util.Locale;
import java.util.TimeZone;

import net.fortuna.ical4j.model.Date;
import net.fortuna.ical4j.model.TimeZoneRegistry;
import net.fortuna.ical4j.model.TimeZoneRegistryFactory;
import net.fortuna.ical4j.model.parameter.Value;
import net.fortuna.ical4j.model.property.DtStart;
import net.fortuna.ical4j.util.Dates;

import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.joda.time.format.DateTimeFormat;
import org.junit.BeforeClass;
import org.junit.Test;

import com.ecco.calendar.dom.MedDate;
import com.ecco.calendar.core.util.DateTimeUtils;

/**
 * Check the expectations around default timezones - particularly to identify the issue with ical4j.
 */
public class UTCTest {

    private static final TimeZoneRegistry TIMEZONE_REGISTRY = TimeZoneRegistryFactory.getInstance().createRegistry();

    /**
     * This class did set the default timezone in case its not set using -Duser.timezone=UTC, but we force the jvm param
     * to be set prior to this test as its a mutable change which can cause difficulty int the repeatability of tests.
     * We also want the testing environment to also ensure we are using the right timezone. For instance, the maven
     * surefire plugin needs configuring using argLine in maven-surefire-plugin because surefire forks a new
     * jvm and the global jvm args of MAVEN_OPTS are not passed on - see http://stackoverflow.com/a/4074196.
     * Also see http://maven.apache.org/surefire/maven-surefire-plugin/examples/class-loading.html.
     *
     * The main web server also verifies on startup that the jvm variable is already set using lifecycle hook on bean
     * LegacyConfig.
     */
    @BeforeClass
    public static void verifyDefaultTimezone() {
        displayCurrentTimeZone();
        if (!"UTC".equals(TimeZone.getDefault().getID())) {
            throw new IllegalStateException("JVM param -Duser.timezone=UTC must be specified for the tests "
                    + "(one of java cmd; m2e test VM args; surefire pom argLine (which is now configured to pick up MAVEN_OPTS). "
                    + "The JVM's current timezone is " + TimeZone.getDefault().getID());
        }
    }

    @Test
    public void testLocalDate() throws ParseException {

        // a time in the summer
        LocalDate ld = new LocalDate(2014, 5, 1);
        DateTime dt1 = DateTimeUtils.convertFromUsersLocalDate(ld);

        // LocalDate shows as the Europe/London that we intended
        // local date during the summer (2014/5/1), has correct timezone
        org.junit.Assert.assertEquals("Europe/London", dt1.getZone().toString());
        // local date during the summer (2014/5/1), has correct datetime
        org.junit.Assert.assertEquals("Thursday, 1 May 2014 00:00:00 o'clock BST", DateTimeFormat.forPattern(DateTimeFormat.patternForStyle("FF", Locale.UK)).print(dt1));

        // LocalDateTime shows as the Europe/London that we intended
        LocalDateTime ldt = new LocalDateTime(2014, 5, 1, 10, 0);
        DateTime dt2 = DateTimeUtils.convertFromUsersLocalDateTime(ldt);
        org.junit.Assert.assertEquals("Thursday, 1 May 2014 10:00:00 o'clock BST", DateTimeFormat.forPattern(DateTimeFormat.patternForStyle("FF", Locale.UK)).print(dt2));

        // as UTC
        DateTime dtu = DateTimeUtils.convertToTz(dt2, DateTimeZone.UTC);
        org.junit.Assert.assertEquals("Thursday, 1 May 2014 09:00:00 o'clock UTC", DateTimeFormat.forPattern(DateTimeFormat.patternForStyle("FF", Locale.UK)).print(dtu));
    }

    @Test
    public void testUTC() throws ParseException {

        System.out.println("");
        System.out.println("");

        // *************************
        // ical4j FUNDAMNENTAL PROBLEM

        // SUMMARY
        // cosmo uses floating times (no timezone specified on the events)
        // but ical4j uses the jdk default timezone which is Europe/London (server does too!)
        // so any dates presented are assuming this timezone - a date object has no tz, its just ms!
        // therefore setting the default tz doesn't change the dates ms, but does the string representation
        // since our cosmo items are floating events (no tz specified) we are ok to change the default tz
        // and we should use the long representation always in our interface with cosmo (assuming it does utc too)
        // so set a default tz in the jdk on startup and use the long representation

        System.out.println("ical4j PROBLEM");

        // the feed comes back with an occurrence 'date'
        // this might not be how cosmo constructs the date, but it produces the same problem for us
        // in fact the date returned is from cosmo date object (not the atom vcalendar text from cosmo_event_stamp)
        // the actual date for a recurring event (which we are testing first) is org.osaf.cosmo.model.NoteOccurrenceInvocation
        // the date passed in there is from StandardItemFilterProcessor which calls NoteOccurrenceUtil.createNoteOccurrence(entry.getValue().getRid(), note)
        // so the date comes from the entry.getValue.getRid() which comes from above it - expander.getOccurrences
        // which solves dates from the event component from org.osaf.cosmo.calendar.InstanceList
        // eg prop=DTSTART;VALUE=DATE:20000329 solved from InstanceList.getStartDate which uses ical4j prop.getDate()
        // for an event of 20100329 it has a ms of 1269817200000 which is Sun, 28 Mar 2010 23:00:00 UTC, not 29 mar!
        // this is the fundamental problems
        DtStart prop = new DtStart("20100329");
        Date dte = prop.getDate();
        // CONFUSED - if we are all utc, why does it appear as utc-1 - as if we are in a different tz
        // because a parameter -user.timezone=utc needs to be specified at the jvm level!
        System.out.println("ical4j getDate raw: " + dte);
        System.out.println("ical4j getDate raw ms: " + dte.getTime());

        System.out.println("");
        System.out.println("");
        System.out.println("");

        // *************************
        // COSMO ical4j date -> JODA
        // on jan 2010, days after mar 29 2010 shows as a day before, since DST due to converting ical4j into joda
        System.out.println("COSMO -> MED");

        net.fortuna.ical4j.model.Date fortunaDate;
        try {
            fortunaDate = new net.fortuna.ical4j.model.Date("20100329");
        } catch (ParseException e) {
            return;
        }

        // date - ical date timestamp is -1hour in utc to joda!
        System.out.println("fortuna date raw: " + fortunaDate);
        System.out.println("fortuna date raw ms: " + fortunaDate.getTime());
        DateTime converted = new DateTime(fortunaDate.getTime());
        System.out.println("fortuna date into joda: " + converted);
        converted = new DateTime(fortunaDate.getTime(), DateTimeZone.UTC);
        System.out.println("fortuna date into joda UTC: " + converted);
        System.out.println("because joda default tz: " + DateTimeZone.getDefault().getID());


        // the problem is "As a rule you can not convert a java.util.Date to a different time zone" - http://www.odi.ch/prog/design/datetime.php
        // because date is always utc - therefore the utc we get back from cosmo is wrong
        // since the date.getTime() return ms which is an hour earlier
        // we can get the representation through a calendar

        // adapted from out of the box code
        java.util.TimeZone tz = java.util.TimeZone.getTimeZone("UTC");
        Calendar start = Calendar.getInstance(tz); // we ignore locale
        // since a date is meant to represent a utc moment, we set the utc milis
        start.setTimeInMillis(fortunaDate.getTime());
        //start.set(Calendar.YEAR, 2010);
        //start.set(Calendar.MONTH, 2);
        //start.set(Calendar.DAY_OF_MONTH, 29);
        //start.set(Calendar.HOUR_OF_DAY, 0);
        //start.set(Calendar.MINUTE, 0);
        //start.set(Calendar.SECOND, 0);
        // convert back to a date
        net.fortuna.ical4j.model.DateTime startDate = (net.fortuna.ical4j.model.DateTime) Dates.getInstance(start.getTime(), Value.DATE_TIME);
        net.fortuna.ical4j.model.DateTime startDate2 = new net.fortuna.ical4j.model.DateTime(true);
        startDate2.setTime(startDate.getTime());
        //java.util.TimeZone tzUtc = java.util.TimeZone.getTimeZone("UTC");
        //startDate.setTimeZone(TIMEZONE_REGISTRY.getTimeZone(tz.getID()));

        // startDate here works because the ms value is utc, and our calendar is utc (ignoring jdk defaults)
        // and the textual representation is in the current timezone - probably europe/london!
        System.out.println("fortuna recreated date UTC: " + startDate);
        System.out.println("fortuna recreated date isUTC: " + startDate.isUtc());
        System.out.println("fortuna recreated date2 UTC: " + startDate2);
        System.out.println("fortuna recreated date2 isUTC: " + startDate2.isUtc());

        System.out.println("");
        System.out.println("");
        System.out.println("");


        // *************************
        // MED/JODA date -> COSMO ical4j
        // before created ical4j with utc, it was out by an hour due to DST (jan 2010 creating event on mar29 2010)
        // this would affect existing created events if there was a time specfied, but only released a few days and automatic conversion was allDay
        System.out.println("MED/JODA -> COSMO");

        // test the itemeventconvertor
        // this saves MedDates to an item
        MedDate medDate = new MedDate(false, true, true, true);
        medDate.setDay((short)29);
        medDate.setMonth((short)3);
        medDate.setYear(2010);
        DateTimeZone timeZone = DateTimeZone.UTC;
        boolean isRepeatYears = true;

        // med to joda should be smooth since the date is constructed in the given timezone
        // so 29/mar/2010 becomes that local date in whatever timezone we specify
        org.joda.time.DateTime jodaStart = null;
        if (isRepeatYears) {
            jodaStart = DateTimeUtils.convertToDateTime(medDate, false, medDate.hasTime(), timeZone);
        } else {
            jodaStart = DateTimeUtils.convertToDateTime(medDate, true, medDate.hasTime(), timeZone);
        }
        // true here means its a utc date - the source code says "Updates this date-time to display in UTC time if the argument is true. Otherwise, resets to the default timezone."
        // the default timezone is utc, so we are ok
        net.fortuna.ical4j.model.DateTime icalDate = new net.fortuna.ical4j.model.DateTime(true);
        icalDate.setTime(jodaStart.getMillis());
        //org.joda.time.DateTime jodaEnd = DateTimeUtils.convertToDateTime(event.getEventEndDate(), true, event.getEventDate().hasTime(), timeZone);

        System.out.println("joda date: " + jodaStart);
        System.out.println("fortuna date: " + icalDate);

    }

    private static void displayCurrentTimeZone() {
        // gmt almost = utc since both are counters, our timezone is gmt+1 in summer etc
        // but gmt != utc exactly (see reference in http://www.quickfixj.org/jira/browse/QFJ-264)
        System.out.println("calendar default TZ: " + java.util.TimeZone.getDefault());
        System.out.println("fortuna default TZ (docs say gmt - and build on top of jdk): " + java.util.TimeZone.getDefault());
        // the reason the isUTC is false is because UTC doesn't match 'Etc/UTC' or 'GMT' !!
        // see net.fortuna.ical4j.util.TimeZones isUtc
        System.out.println("fortuna default isUtc: " + new net.fortuna.ical4j.model.DateTime().isUtc() + " [net.fortuna.ical4j.util.TimeZones.isUtc matches 'Etc/UTC' or 'GMT', but not 'UTC' - crazy stuff]");
    }

}
