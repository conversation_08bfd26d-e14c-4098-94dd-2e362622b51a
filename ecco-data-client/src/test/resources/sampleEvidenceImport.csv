mappingRow,firstRow,lastRow,spELOperationsEnabled,baseUrl,,,
6,11,11,TRUE,http://localhost:8888/ecco-war,,,
,,,,^^ need to also change in ClientConfig.java,,,
,,,,,,,
,,,,,commentConcat allows us to make one 'comment' out of several fields,,
srId,referralCode,workDate,evidenceTask,flagIds,commentConcat['Notes'],commentConcat['Thoughts'],commentConcat['Reaction']
,,#this.equals('NULL') ? null : #this,'needsAssessment',,,,
,,,,,,,
,,,,,,,
,,,,,,,
200023,2027,07/03/03,,"1011,1022",This and this for this note,Some thoughts,
