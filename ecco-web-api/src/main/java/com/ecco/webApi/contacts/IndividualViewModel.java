package com.ecco.webApi.contacts;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.text.StrBuilder;
import org.joda.time.DateTime;

import com.ecco.dom.PreferredContactMethod;


// Property accessors are unfortunately required for com.ecco.data.client.csv.CSVBeanReader.
// See ECCO-703
@Getter
@Setter
public class IndividualViewModel extends ContactViewModel {

    public String jobTitle;

    public String title;

    public String firstName;

    public String lastName;

    public String knownAs;

    public Integer pronounsId;

    public String mobileNumber;

    public PreferredContactMethod preferredContactMethod;

    public Long organisationId;

    /** Organisation - only used client side for parsing */
    public AgencyViewModel organisation;

    /** This may just be useful, so I've added it */
    public String calendarId;

    /**
     * True if there is a user account associated with this contact
     */
    public boolean isUser;

    /** When this user last logged in - null if never has */
    public DateTime userLastLoggedIn;


    /** Helper method to work out a code for an individual based on business
     * logic of oragnisationId and name
     */
    public String createUniqueEnoughCode() {
        StrBuilder out = new StrBuilder();
        out.append(organisationId);
        out.append(firstName);
        out.append(lastName);
        String outStr = StringUtils.abbreviate(out.toString(), 255);
        return outStr;
    }

}
