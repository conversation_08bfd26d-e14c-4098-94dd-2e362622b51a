import BaseAsyncCommandForm = require("../cmd-queue/BaseAsyncCommandForm");
import Form = require("../controls/Form");
import InputGroup = require("../controls/InputGroup");
import TextInput = require("../controls/TextInput");
import commands = require("./commands");
import {apiClient} from "ecco-components";
import {ServiceType as ServiceTypeDto} from "ecco-dto/service-config-dto";
import {showFormInModalDom} from "../components/MUIConverterUtils";
import {ServiceTypeAjaxRepository} from "ecco-dto";

var repository = new ServiceTypeAjaxRepository(apiClient);


class EditServiceTypeForm extends BaseAsyncCommandForm<ServiceTypeDto> {

    public static showInModal(serviceTypeId: number) {
        var form = new EditServiceTypeForm(serviceTypeId);
        form.load();
        showFormInModalDom(form);
    }

    private form = new Form().addClass("form-30-50");
    private name = new TextInput("service type name");
    private origDto: ServiceTypeDto;

    /** We provide the form with current state, and we get back a CommandQueue
     *  containing the changes to send/apply (if we click done).
     */
    constructor(private serviceTypeId: number) {
        super(!serviceTypeId ? "add new service type" : "edit service type");
        this.form
            .append( new InputGroup("service type name", this.name) );
    }

    protected fetchViewData(): Promise<ServiceTypeDto> {
        if (this.serviceTypeId) {
            return repository.findOneServiceTypeDto(this.serviceTypeId);
        }
        return Promise.resolve(null);
    }

    protected render(serviceTypeDto: ServiceTypeDto) {
        this.origDto = serviceTypeDto;

        if (serviceTypeDto) {
            this.name.setVal(serviceTypeDto.name);
        }

        this.enableSubmit(); // probably want to have this be linked to length of commandQueue (based on event?)
        this.element().empty();
        this.append(this.form);
    }


    protected submitForm(): Promise<void> {
        var cmd;
        if (this.origDto) {
            cmd = new commands.ServiceTypeChangeCommand("update", this.origDto.id)
                .changeName(this.origDto.name, this.name.val());
        }
        else {
            cmd = new commands.ServiceTypeChangeCommand("add", null)
                .changeName(null, this.name.val());
        }

        if (cmd.hasChanges()) {
            this.commandQueue.addCommand(cmd);
        }
        return super.submitForm();
    }

}

export = EditServiceTypeForm;
