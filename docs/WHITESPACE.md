
Whitespace handling is a bit of a pain.

If commits get into the code base with large changes in whitespace, it will make merging changes from 
parallel branches a pain.

The solution to having introduced CRLF into the repository is to do:

    git branch branchtofix-whitespacefix branchtofix

    git filter-branch --force --tree-filter 'git ls-files | xargs file | sed -n -e "/.*: .*text.*/s/\(.*\): .*/\1/p" | xargs dos2unix' --tag-name-filter cat -- main..branchtofix-whitespacefix
