<?xml version="1.0" encoding="UTF-8"?>
<suppressions xmlns="https://jeremylong.github.io/DependencyCheck/dependency-suppression.1.3.xsd"
              xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
              xsi:schemaLocation="https://jeremylong.github.io/DependencyCheck/dependency-suppression.1.3.xsd
              https://jeremylong.github.io/DependencyCheck/dependency-suppression.1.3.xsd"
>
    <!-- NOTE: To use this, we need -Djavax.xml.accessExternalSchema=all -->
    <suppress>
        <notes><![CDATA[
   file name: jackrabbit-jcr-webdav-1.0-osaf-20061023.jar
   "allows remote attackers to read arbitrary files and send requests to intranet servers via a crafted WebDAV request."
   We don't expose WebDAV endpoints.
   ]]></notes>
        <packageUrl>pkg:maven/org.apache.jackrabbit/jackrabbit-jcr-webdav@1.0-osaf-20061023</packageUrl>
        <cve>CVE-2015-1833</cve>
    </suppress>
    <suppress>
        <notes><![CDATA[
   file name: guava-29.0-jre.jar
   Guava has an API for creating temporary files which sets permissions as visible to all.  This is deprecated but
   not removed in future versions, and we should use java.nio.file.Files.createTempDirectory() which explicitly
   configures permissions of 700.
   Supression reason: No usages of this method are found in our code or any libraries we use.
   ]]></notes>
        <packageUrl>pkg:maven/com.google.guava/guava@29.0-jre</packageUrl>
        <cve>CVE-2020-8908</cve>
    </suppress>
    <suppress>
        <notes><![CDATA[
   file name: h2-1.4.200.jar
   Supression reason: We don't use H2 in production
   ]]></notes>
        <packageUrl>pkg:maven/com.h2database/h2@1.4.200</packageUrl>
        <cve>CVE-2021-23463</cve>
        <cve>CVE-2021-42392</cve>
        <cve>CVE-2022-23221</cve>
    </suppress>
</suppressions>