package com.ecco.serviceConfig.service;

import static java.util.stream.Collectors.toList;

import java.util.List;
import java.util.stream.StreamSupport;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.ecco.infrastructure.annotations.ReadOnlyTransaction;
import com.ecco.serviceConfig.dom.OutcomeSupport;
import com.ecco.serviceConfig.dom.OutcomeThreat;
import com.ecco.serviceConfig.dom.QuestionGroupSupport;
import com.ecco.serviceConfig.repositories.OutcomeRepository;
import com.ecco.serviceConfig.repositories.QuestionGroupRepository;
import com.ecco.serviceConfig.viewModel.OutcomeToViewModel;
import com.ecco.serviceConfig.viewModel.OutcomeViewModel;
import com.ecco.serviceConfig.viewModel.QuestionGroupToViewModel;
import com.ecco.serviceConfig.viewModel.QuestionGroupViewModel;
import com.ecco.serviceConfig.viewModel.RiskAreaToViewModel;
import com.ecco.serviceConfig.viewModel.RiskAreaViewModel;

@Service("sessionDataService")
@ReadOnlyTransaction
public class SessionDataServiceImpl implements SessionDataService {

    private final OutcomeToViewModel outcomeToViewModel = new OutcomeToViewModel();
    private final RiskAreaToViewModel riskAreaToViewModel = new RiskAreaToViewModel();
    private final QuestionGroupToViewModel questionGroupToViewModel = new QuestionGroupToViewModel();

    private final OutcomeRepository outcomeRepository;
    private final QuestionGroupRepository questionGroupRepository;

    public SessionDataServiceImpl() {
        // Required for CGLIB - we only need services wired on the proxy's target
        this(null, null);
    }

    @Autowired
    public SessionDataServiceImpl(OutcomeRepository outcomeRepository,
                                  QuestionGroupRepository questionGroupRepository) {
        super();
        this.outcomeRepository = outcomeRepository;
        this.questionGroupRepository = questionGroupRepository;
    }

    @Cacheable(value="outcomeViewModels", key="'*'")
    @Override
    public List<OutcomeViewModel> findOutcomes() {
        Iterable<OutcomeSupport> outcomes = outcomeRepository.findAllSupport();
        return StreamSupport.stream(outcomes.spliterator(), false).map(outcomeToViewModel).collect(toList());
    }

    @Cacheable(value="riskAreaViewModels", key="'*'")
    @Override
    public List<RiskAreaViewModel> findRiskAreas() {
        Iterable<OutcomeThreat> riskAreas = outcomeRepository.findAllThreat();
        return StreamSupport.stream(riskAreas.spliterator(), false).map(riskAreaToViewModel::apply).collect(toList());
    }

    @Cacheable(value="questionGroupViewModels", key="'*'")
    @Override
    public List<QuestionGroupViewModel> findQuestionGroups() {
        Iterable<QuestionGroupSupport> questionGroups = questionGroupRepository.findAll();
        return StreamSupport.stream(questionGroups.spliterator(), false).map(questionGroupToViewModel::apply).collect(toList());
    }

}
