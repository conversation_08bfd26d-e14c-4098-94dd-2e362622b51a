import View = require("../controls/View");
import WizardState = require("./WizardState");

/**
 * The operations required from a form to be used by WizardForm
 * @deprecated - need to implement in React
 */
interface WizardView extends View {

    /**
     * Identify which page should be shown.  Normally returns zero, but for resuming a saved wizard state, may
     * be non-zero
     */
    getWizardInitialPage(): number;

    /**
     * Handle the rendering of the page
     */
    renderWithPage(i: number): void;

    /**
     * Handle the rendering of a cancel page
     */
    renderWithCancelConfirmation(): void;

    /**
     * Set a callback for the form to get the state of the wizard
     */
    setGetWizardState(getState: () => WizardState): void;

    /**
     * Set a callback for the form to trigger the next page
     */
    setWizardTriggerPage(trigger: () => void): void;

    /**
     * Set a callback for the form to trigger the previous page
     */
    setWizardPreviousPage(trigger: () => void): void;

    /**
     * Redraws a page (perhaps after a cancel page)
     */
    setWizardRerenderPage(trigger: () => void): void;

    /**
     * Set a callback for the form to trigger a cancel page
     */
    setWizardCancelPage(trigger: () => void): void;

    /**
     * Perform the action when the user has finished the wizard
     * (eg save the changes)
     */
    wizardCompleted(): void;

}
export = WizardView;
