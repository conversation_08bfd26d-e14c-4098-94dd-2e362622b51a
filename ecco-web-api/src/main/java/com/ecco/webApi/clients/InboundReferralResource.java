package com.ecco.webApi.clients;

import com.ecco.dom.ClientDetail;
import com.ecco.infrastructure.rest.hateoas.schema.JsonSchemaMetadata;
import com.ecco.infrastructure.rest.hateoas.schema.JsonSchemaProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.joda.time.LocalDate;
import org.springframework.hateoas.RepresentationModel;

import javax.validation.constraints.NotNull;

import static lombok.AccessLevel.PROTECTED;


/**
 * External referral API - creates a referral without a login.
 * Available at /context/api/inbound/referrals/$schema/
 */

@NoArgsConstructor(access = PROTECTED)
@AllArgsConstructor
@Getter
@Builder
public class InboundReferralResource extends RepresentationModel<InboundReferralResource> {

    @NotNull
    @JsonSchemaMetadata(title = "service", order = 10, description = "please select a service")
    @JsonSchemaProperty(enumExpression = "#{@servicesService.findAllInboundServicesAsMap()}") // returns Map<String (id), String (name)>
    private Long serviceId;

    @JsonSchemaMetadata(title = "project", order = 10)
    // schema list depends on service, so not so easy - should migrate to svcCat
    private Long projectId;

    @NotNull
    @JsonSchemaMetadata(title = "client first name", order = 20)
    @JsonSchemaProperty(pattern = "^.{1,128}$")
    private String firstName; // size 255 (cosmo 128)

    @NotNull
    @JsonSchemaMetadata(title = "client last name", order = 30)
    @JsonSchemaProperty(pattern = "^.{1,128}$")
    private String lastName; // size 255 (cosmo 128)

    @NotNull
    @JsonSchemaMetadata(title = "client known as", order = 32)
    private String knownAs;

    // gender (drop-down)
    @JsonSchemaMetadata(title = "client pronoun", order = 34)
    // TODO @JsonSchemaProperty(enumRef="api/inbound/list/pronouns")
    private Long pronounsId;

    @JsonSchemaMetadata(title = "client mobile number", order = 35)
    private String mobileNumber; // size 255

    @JsonSchemaMetadata(title = "client landline number", order = 35)
    private String phoneNumber; // size 255

    @JsonSchemaMetadata(title = "client email number", order = 35)
    // NB cosmo may also have an opinion here (size 128)
    @JsonSchemaProperty(pattern = ".+@.+\\..+") // TODO limit to 128
    private String email; // size 255

    // preferred contact (drop-down)
    // consent? - textMap?

    @JsonSchemaMetadata(title = "client date of birth", description = "eg 2018-06-01", order = 40)
    private LocalDate birthDate;

    // gender (drop-down)
    @JsonSchemaMetadata(title = "client gender", order = 40)
    // TODO @JsonSchemaProperty(enumRef="api/inbound/list/genders")
    private Long genderId;

    @JsonSchemaMetadata(title = "client address line1", order = 40)
    private String line1;

    @JsonSchemaMetadata(title = "client address town", order = 40)
    private String town;

    @JsonSchemaMetadata(title = "client address postcode", order = 40)
    private String postCode;

    // country (drop-down)

    // LIST_LANGUAGES is not constant enough - because of toUri, toString
    //private static final String LIST_LANGUAGES = linkToApi(methodOn(SessionDataController.class).listLanguages()).toUri().toString();
    @JsonSchemaMetadata(title = "client first language", order = 40, description = "please select a language")
    @JsonSchemaProperty(enumExpression = "#{@idNameService.findAllInboundLanguages()}") // returns Map<String (id), String (name)>
    private Integer firstLanguageId;

    @JsonSchemaMetadata(title = "client ethnic origin", order = 40)
    @JsonSchemaProperty(enumExpression = "#{@idNameService.findAllInboundEthnicOrigins()}") // returns Map<String (id), String (name)>
    private Integer ethnicOriginId;

    @JsonSchemaMetadata(title = "client religion", order = 40)
    @JsonSchemaProperty(enumExpression = "#{@idNameService.findAllInboundReligions()}") // returns Map<String (id), String (name)>
    private Integer religionId;

    // TODO disability (drop-down)
    private Integer disabilityId;

    // TODO sexual orientation (drop-down)
    private Integer sexualOrientationId;

    private Integer nationalityId;

    private Integer maritalStatusId;

    private String housingBenefit;

    // NINO (specific rules)
    @JsonSchemaMetadata(title = "client NINO", order = 40)
    @JsonSchemaProperty(pattern = ClientDetail.NINO_REGEX)
    private String ni;

    // NHS (specific rules)
    @JsonSchemaMetadata(title = "client NHS", order = 42)
    @JsonSchemaProperty(pattern = ClientDetail.NHS_REGEX)
    private String nhs;

    @JsonSchemaMetadata(title = "reason for referral", order = 50)
    private String referralReason;

    // SHARED
    @JsonSchemaMetadata(title = "self referral", order = 50)
    private Boolean selfReferral;

    @JsonSchemaMetadata(title = "referrer's name", description = "i.e. firstname lastname", order = 60)
    private String referrerName;

    @JsonSchemaMetadata(title = "referrer's job", order = 60)
    private String referrerJobTitle;

    @JsonSchemaMetadata(title = "referrer's phone number", order = 70)
    private String referrerPhoneNumber;

    @JsonSchemaMetadata(title = "referrer's email number", order = 75)
    @JsonSchemaProperty(pattern = ".+@.+\\..+") // TODO limit to 128
    private String referrerEmail;

    @JsonSchemaMetadata(title = "referrer's organisation name", order = 80)
    private String agencyName;

}
