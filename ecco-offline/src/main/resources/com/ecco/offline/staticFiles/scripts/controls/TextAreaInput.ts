import $ = require("jquery");
import StringInputControl = require("./StringInputControl");
import {getAutoSaveValue, setAutoSaveValue, setAutoSaveExpiry} from "./autosave";

class TextAreaInput extends StringInputControl {

    private $status: $.JQuery;
    private $placeholder: $.JQuery;
    private autoSaveEnabled: boolean;
    private tapToEdit: boolean;

    /**
     * @param tapToEdit true if to use contenteditable instead of an input control
     */
    public constructor(id: string, rows: number = 4, tapToEdit: boolean = false, classes: string = "form-control") {
        const $input = tapToEdit ? $("<div>")
                .addClass(classes + " textarea")
                .prop('contenteditable', true)
            : $("<textarea>")
                .addClass(classes)
                .attr("rows",rows.toString());

        const $element = $("<span>");
        super($element, $input, id);
        this.tapToEdit = tapToEdit;
        this.$status = $("<span>").addClass("text-muted").hide();

        if (tapToEdit) {
            // input is HTML5 so won't work for older IE, we can use keypress
            $input.on('input propertychange', event => this.handleChange(event));
            $input.on('keypress', event => this.trimMaxLength(event));
            this.$placeholder = $("<span>").addClass("text-muted placeholder").hide();
        }

        $element
            .append(this.$placeholder)
            .append($input)
            .append(this.$status);
    }


    public placeholderText(text: string): this {
        if (this.tapToEdit) {
            this.$placeholder.text(text).show();
        }
        return super.placeholderText(text);
    }

    protected setValOnInput($elem: $.JQuery, value: string) {
        if (this.tapToEdit) {
            $elem.text(value);
            $elem.trigger('change');
        } else {
            super.setValOnInput($elem, value);
        }
    }

    protected valOnInput(): string {
        // innerText preserves newlines
        // see https://stackoverflow.com/questions/13762863/contenteditable-field-to-maintain-newlines-upon-database-entry
        // and https://caniuse.com/#search=innerText
        return this.tapToEdit ? this.$input[0].innerText : super.valOnInput();
    }

    public withAutoSave(): TextAreaInput {
        this.autoSaveEnabled = true;
        const saved = getAutoSaveValue(this.autoSaveKey());
        if (saved) {
            this.setVal(saved);
            this.$status.text("draft restored").show();
        }
        return this;
    }

    protected autoSave(val: string) {
        if (!this.autoSaveEnabled) { return; }

        const key = this.autoSaveKey();
        const saved = getAutoSaveValue(key);
        if (saved != val) {
            setAutoSaveValue(key, val);
            setAutoSaveExpiry();
            this.$status.text("draft saved").show()
                .fadeOut(1500, () => {
                    if (this.tapToEdit && this.placeholder) {
                        this.$status.text(this.placeholder).fadeIn(500);
                    }
            });
        }
    }
}

export = TextAreaInput;
