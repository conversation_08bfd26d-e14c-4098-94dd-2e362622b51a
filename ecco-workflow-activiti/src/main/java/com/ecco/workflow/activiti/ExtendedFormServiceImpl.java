package com.ecco.workflow.activiti;

import org.activiti.engine.form.TaskFormData;
import org.activiti.engine.impl.FormServiceImpl;

/**
 * Extended the Activiti form service to allow form data for non-active tasks to be retrieved.
 */
public class ExtendedFormServiceImpl extends FormServiceImpl implements ExtendedFormService {
    public TaskFormData getTaskFormData(String processInstanceId, String taskDefinitionKey) {
      return commandExecutor.execute(new GetTaskFormCmd(processInstanceId, taskDefinitionKey));
    }

    @Override
    public TaskFormData getTaskFormDataForProcessDefinition(String processDefinitionId, String taskDefinitionKey) {
        return commandExecutor.execute(new GetTaskDefinitionFormCmd(processDefinitionId, taskDefinitionKey));
    }
}
