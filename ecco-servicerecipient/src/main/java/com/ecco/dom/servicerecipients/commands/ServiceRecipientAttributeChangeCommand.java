package com.ecco.dom.servicerecipients.commands;

import com.ecco.dom.servicerecipients.ServiceRecipientCommand;
import org.joda.time.Instant;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import java.util.UUID;

@Entity
@DiscriminatorValue("attributeChange")
public class ServiceRecipientAttributeChangeCommand extends ServiceRecipientCommand {

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    public ServiceRecipientAttributeChangeCommand() {
        super();
    }

    public ServiceRecipientAttributeChangeCommand(@Nullable UUID uuid, @Nonnull Instant remoteCreationTime,
                                                  long userId, @Nonnull String body, int serviceRecipientId) {
        super(uuid, remoteCreationTime, userId, body, serviceRecipientId);
    }

}
