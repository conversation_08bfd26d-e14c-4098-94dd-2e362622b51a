# Reference data mapping between external system and ecco

# CONVERTING FROM external system (eg result of queryClientsByExample)
# external system <DOMAIN>.<VALUE> = ecco business key

# CONVERTING FROM ecco (eg parameter of queryClientsByExample)
# obtains business key from ecco
# <ECCO>.<DOMAIN>.<BUSKEY> = external system <VALUE>

# CONVERTING FROM ecco (eg create - NB updateClient just passes the BUSKEY through to create)
# obtains business key from ecco
# <ECCO>.<DOMAIN>.<BUSKEY> = external system <VALUE>

# CONVERTING FROM ecco NULL if needed to force overwrite (eg update<PERSON>lient needs a BUSKEY through to create)
# NB This is effectively a double lookup, since the update passes through create, which gets the ECCO.DOMAIN.BUSKEY
# <ECCO>.<DOMAIN>.DEFAULT = <BUSKEY>


FLAG_ALERT.1=1048
#FLAG_ALERT.2=
#FLAG_ALERT.3=
#FLAG_ALERT.4=
#FLAG_ALERT.5=
#FLAG_ALERT.6=
#FLAG_ALERT.7=
#FLAG_ALERT.8=
#FLAG_ALERT.9=

ECCO.FLAG_ALERT.1048=1

FLAG_IMPAIR.ACCESS - SEE ILO=1048
ECCO.FLAG_IMPAIR.1048=ACCESS - SEE ILO

# DISABILTY shares FLAG_IMPAIR
FLAG_IMPAIR.YES=YES
FLAG_IMPAIR.NO=NO
FLAG_IMPAIR.Y=YES
FLAG_IMPAIR.N=NO
FLAG_IMPAIR.REFUSED=REFUSED

# Non-current values
#ETHNIC_ORIGIN.BLACK - no mapping
#ETHNIC_ORIGIN.MIXED\ RACE     Mixed: Other
#ETHNIC_ORIGIN.OTHERRACE       Mixed: Other
ETHNIC_ORIGIN.WHITE=WHITE
#ETHNIC_ORIGIN.MIXEDRACE       Mixed: Other
#ETHNIC_ORIGIN.NOTONACAD       Missing
#ETHNIC_ORIGIN.OTHER           Chinese/Other: Other
#ETHNIC_ORIGIN.MIXED           Mixed: Other
ETHNIC_ORIGIN.ASIAN=ASIAN
ETHNIC_ORIGIN.CHINOTHER=CHINOTHER
# Current values
#ETHNIC_ORIGIN.REFUSED         Do not wish to disclose
ETHNIC_ORIGIN.WHITEBRIT=WHITEBRIT
ETHNIC_ORIGIN.WHITEIRISH=WHITEIRISH
ETHNIC_ORIGIN.OTHERWHITE=OTHERWHITE
ETHNIC_ORIGIN.CHINESE=CHINESE
#ETHNIC_ORIGIN.INDIAN          Asian/Asian British: Indian
#ETHNIC_ORIGIN.PAKISTANI       Asian/Asian Birtish: Pakistani
#ETHNIC_ORIGIN.BANGLADESH      Asian/Asian British: Bangladeshi
#ETHNIC_ORIGIN.OTHERASIAN      Asian/Asian British: Other
#ETHNIC_ORIGIN.CARIBBEAN       Black/Black British: Caribbean
#ETHNIC_ORIGIN.AFRICAN         Black/Black British: African
#ETHNIC_ORIGIN.OTHERBLACK      Black/Black British: Other
#ETHNIC_ORIGIN.MIXEDWBCAR      Mixed: White & Black Caribbean
#ETHNIC_ORIGIN.MIXEDWBAFR      Mixed: White & Black African
#ETHNIC_ORIGIN.MIXEDWHIAS      Mixed: White & Asian
#ETHNIC_ORIGIN.OTHERMIXED      Mixed: Other
#ETHNIC_ORIGIN.OTHEROTHER      Chinese/Other: Other
#ETHNIC_ORIGIN.WHITESCOT       White: Other
#ETHNIC_ORIGIN.WHITEOBRIT      White: British

# used in testing ApplicationTests which use the liquibase changelog-master.xml
ETHNIC_ORIGIN.GYPSY=GYPSY

#ETHNIC_ORIGIN.NOTPERSON       Missing
#ETHNIC_ORIGIN.ARAB            Arabic: Other
ECCO.ETHNIC_ORIGIN.DEFAULT=REFUSED

RELIGION.CHRISTIAN=CHRISTIAN
#RELIGION.HINDU      Hindu
#RELIGION.MUSLIM     Muslim
#RELIGION.SIKH       Sikh
RELIGION.JEWISH=JEWISH
RELIGION.BUDDHIST=BUDDHIST
#RELIGION.REFUSED    Not disclosed
#RELIGION.OTHER      Other
#RELIGION.NORELIGION None
#RELIGION.NOTPERSON  Missing
ECCO.RELIGION.DEFAULT=NOTPERSON

GENDER.MALE=MALE
GENDER.FEMALE=FEMALE
GENDER.TRANGENDER=TRANSGENDER
GENDER.UNKNOWN=UNKNOWN
GENDER.NOTPERSON=NOTPERSON
GENDER.DEFAULT=UNKNOWN
ECCO.GENDER.MALE=MALE
ECCO.GENDER.FEMALE=FEMALE
ECCO.GENDER.TRANGENDER=TRANSGENDER
ECCO.GENDER.UNKNOWN=UNKNOWN
ECCO.GENDER.NOTPERSON=NOTPERSON
ECCO.GENDER.DEFAULT=UNKNOWN

SEXUAL_ORIENTATION.HETERO=HETERO
SEXUAL_ORIENTATION.GAY=GAY
SEXUAL_ORIENTATION.LESBIAN=LESBIAN
SEXUAL_ORIENTATION.BISEXUAL=BISEXUAL
# SEXUAL_ORIENTATION.TRANSGEND - no mapping
SEXUAL_ORIENTATION.NOTSTATED=NOTSTATED
SEXUAL_ORIENTATION.NOTPERSON=NOTPERSON
ECCO.SEXUAL_ORIENTATION.DEFAULT=HETERO

# LANGUAGE.ALBANIAN - no mapping
# LANGUAGE.AMHARIC - no mapping
# LANGUAGE.ARABIC - no mapping
# LANGUAGE.BENGALI - no mapping
LANGUAGE.CANTONESE=CANTONESE
# LANGUAGE.CROATIAN - no mapping
# LANGUAGE.CZECH - no mapping
LANGUAGE.ENGLISH=ENGLISH
# LANGUAGE.FARSI - no mapping
# LANGUAGE.FRENCH - no mapping
# LANGUAGE.GERMAN - no mapping
# LANGUAGE.GREEK - no mapping
# LANGUAGE.GUJARATI - no mapping
#LANGUAGE.HINDI - no mapping
# LANGUAGE.ITALIAN - no mapping
# LANGUAGE.KURDISH - no mapping
LANGUAGE.MANDARIN=MANDARIN
# LANGUAGE.POLISH - no mapping
# LANGUAGE.PORTUGESE - no mapping
# LANGUAGE.PUNJABI - no mapping
# LANGUAGE.ROMANIAN - no mapping
# LANGUAGE.RUSSIAN - no mapping
# LANGUAGE.SERBIAN - no mapping
# LANGUAGE.SOMALI - no mapping
# LANGUAGE.SPANISH - no mapping
# LANGUAGE.TAMIL - no mapping
#LANGUAGE.TURKISH - no mapping
LANGUAGE.URDU=URDU
# LANGUAGE.VIETNAMESE - no mapping
# LANGUAGE.ARMENIAN - no mapping
# LANGUAGE.BULGARAIN - no mapping
# LANGUAGE.BURMESE - no mapping
# LANGUAGE.DUTCH - no mapping
# LANGUAGE.ESTONIAN - no mapping
# LANGUAGE.GAELIC - no mapping
# LANGUAGE.GEORGIAN - no mapping
# LANGUAGE.HAUSA - no mapping
# LANGUAGE.HUNGARIAN - no mapping
# LANGUAGE.IBO - no mapping
# LANGUAGE.INDONESIAN - no mapping
# LANGUAGE.LINGALA - no mapping
# LANGUAGE.MALAY - no mapping
# LANGUAGE.NEPALI - no mapping
# LANGUAGE.PUSHTO - no mapping
# LANGUAGE.SERBOCROAT - no mapping
# LANGUAGE.SLOVAK - no mapping
# LANGUAGE.SLOVENIAN - no mapping
# LANGUAGE.SWAHILI - no mapping
# LANGUAGE.TELUGU - no mapping
# LANGUAGE.THAI - no mapping
# LANGUAGE.TIGRINIA - no mapping
# LANGUAGE.WELSH - no mapping
# LANGUAGE.YORUBA - no mapping
# LANGUAGE.CHICHEWA - no mapping
# LANGUAGE.BSL - no mapping
# LANGUAGE.LITHUANIAN - no mapping
ECCO.LANGUAGE.DEFAULT=URDU