import $ = require("jquery");

import BaseControl = require("../controls/BaseControl");
import ErrorControl = require("../controls/ErrorControl");
import {loginPageAuthErrorHandler} from "ecco-offline-data";
import Spinner = require("../controls/Spinner");
import {ResizeEvent} from "@eccosolutions/ecco-common";


abstract class BaseAsyncDataControl<ITEM> extends BaseControl {

    private $spinner: $.JQuery;
    protected loading: boolean; // this will assume false

    /**
     * @param $suppliedContainer if not supplied, a &lt;div class='async-control'&gt; is used.
     */
    constructor($suppliedContainer?: $.JQuery) {
        var $container = $suppliedContainer ||
            $("<div>").addClass("async-control")
            .append(
                $("<div>").addClass("content").text("click to load").click(() => this.load())
            );
        super($container);
        // FIXME: Nice idea, but we need to remove the handler when the control is in a form that gets shown and then
        //  destroyed, otherwise we leak this object.  Weak refs would be nice.
//        ReloadEvent.bus.addHandler(() => this.load());
    }

    /** Trigger async load and render to the container */
    public load(clearElementOnLoad = true) {

        if (this.loading) {
            return;
        }
        this.loading = true;

        if (clearElementOnLoad) {
            this.element()
                .empty(); // this loses event handlers - see https://stackoverflow.com/q/7437229
        }

        // append a new spinner at the bottom
        this.$spinner = this.spinner();
        this.element().append(this.$spinner);

        this.fetchViewData()
            .then( (results) => {
                this.$spinner.remove();
                this.render(results);
                ResizeEvent.bus.fire(new ResizeEvent());
            })
            .catch((error) => {
                if (!loginPageAuthErrorHandler(error)) {
                    this.element()
                            .empty()
                            .append(new ErrorControl("could not load data")
                                    .retry(() => this.load())
                                    .exception(error)
                                    .element());

                    throw error;
                }
            }).finally(() => {
                this.loading = false;
        });
    }

    /**
     * Subclasses can override this to return a different kind of spinner or to wrap a Spinner in other elements.
     * The default implementation just returns <code>new Spinner().element();</code>
     */
    protected spinner() {
        return new Spinner().element();
    };

    protected abstract fetchViewData(): Promise<ITEM>;

    /** Render must replace contents of this.element() */
    protected abstract render(items: ITEM): void;
}
export = BaseAsyncDataControl;
