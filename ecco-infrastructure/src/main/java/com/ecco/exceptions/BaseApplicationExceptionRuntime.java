package com.ecco.exceptions;

public class BaseApplicationExceptionRuntime extends ApplicationExceptionRuntime {

    // the error code of this exception.
    private final String errorCode;

    /**
     * Constructs a new application exception with the given error code.
     * @param errorCode The error code of the exception.
     */
    public BaseApplicationExceptionRuntime(String errorCode) {
        this(errorCode, errorCode);
    }

    /**
     * Constructs a new application exception.
     * @param errorCode The error code of the exception.
     * @param message The message of the exception.
     */
    public BaseApplicationExceptionRuntime(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    /**
     * Constructs a new application exception.
     * @param errorCode The error code of the exception.
     * @param cause The cause for the exception.
     */
    public BaseApplicationExceptionRuntime(String errorCode, Throwable cause) {
        this(errorCode, errorCode, cause);
    }

    /**
     * Constructs a new application exception.
     * @param errorCode The error code of the exception.
     * @param message The message of the exception.
     * @param cause The cause for the exception.
     */
    public BaseApplicationExceptionRuntime(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    /**
     * Returns the error code of this exception.
     * @return The error code of this exception.
     */
    public String getErrorCode() {
        return errorCode;
    }

}
