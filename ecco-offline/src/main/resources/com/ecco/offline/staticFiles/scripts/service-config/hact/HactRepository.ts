import {
    HactOutcomeEvidenceSurveyDto, HactOutcomeMappingDto, HactSocialValueBankDto, HactOutcomeEvidenceActivityDto
} from 'ecco-dto/hact-dto';

export interface HactRepository {

    /** The mappings from actionDefId to HACT outcome codes */
    findAllOutcomeMappings(): Promise<HactOutcomeMappingDto[]>;

    /** The evidence (with questionDefId) to use for the HACT outcomes */
    findAllOutcomeEvidenceSurveys(): Promise<HactOutcomeEvidenceSurveyDto[]>;

    /** The evidence (with actionDefId) to use for the HACT outcomes */
    findAllOutcomeEvidenceActivities(): Promise<HactOutcomeEvidenceActivityDto[]>;

    /** The values of the HACT outcomes */
    findAllSocialValueBank(): Promise<HactSocialValueBankDto[]>;

}
