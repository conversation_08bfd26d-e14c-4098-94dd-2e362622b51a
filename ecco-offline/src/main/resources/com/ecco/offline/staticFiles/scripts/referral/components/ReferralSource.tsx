import * as React from "react"
import {AssociatedContactWizard} from "../../contacts/AssociatedContactWizard";
import {CommandQueue, EditReferralSourceCommand} from "ecco-commands";
import {
    CommandSubform, possiblyModalForm, useCurrentServiceRecipientWithEntities, withCommandForm
} from "ecco-components";
import {ReferralDto, SessionData, SourceType} from "ecco-dto";
import {Agency, Individual} from "ecco-dto/contact-dto";
import {NewReferralWizard} from "./NewReferralWizard";

interface SourceDto {
    serviceRecipientId: number,
    selfReferral?: boolean,
    referrerAgencyId?: number,
    referrerIndividualId?: number
}

function calcSourceType(source: SourceDto): SourceType {
    return source.selfReferral ? "selfReferral" :
        source.referrerAgencyId ? "professional" :
            source.referrerIndividualId ? "individual" : null;
}

export function ReferralSourceDialog(props: {taskHandle: string}) {
    const {resolved: context, reload} = useCurrentServiceRecipientWithEntities()

    let dtoConverted: SourceDto;
    switch (context.serviceRecipient.prefix) {
        case "m":
            dtoConverted = {...context.repair};
            break;
        default:
            dtoConverted = {...context.referral};
    }

    return withCommandForm(commandForm =>
            <ReferralSourceEditor
                sr={dtoConverted}
                sessionData={context.serviceRecipient.features}
                afterSave={reload}
                taskHandle={props.taskHandle}
                commandForm={commandForm}
            />
    );
}

interface EditorState {
    sourceType: SourceType;
    agencyId: number;
    professionalId: number;
    individualId: number;
}

interface EditorProps {
    sr: SourceDto;
    sessionData: SessionData;
    taskHandle: string;
    afterSave: () => void
}

export class ReferralSourceEditor extends CommandSubform<EditorProps, EditorState> {

    constructor(props) {
        super(props);
        this.state = {
            sourceType: calcSourceType(this.props.sr),
            agencyId: this.props.sr.referrerAgencyId,
            professionalId: this.props.sr.referrerAgencyId && this.props.sr.referrerIndividualId,
            individualId: !this.props.sr.referrerAgencyId
                    && this.props.sr.referrerIndividualId
        };
    }

    emitChangesTo(commandQueue: CommandQueue) {
        ReferralSourceEditor.emitChanges(commandQueue, this.props.sr.serviceRecipientId, this.props.taskHandle,
                this.state.sourceType, this.props.sr.selfReferral,
                this.props.sr.referrerAgencyId, this.state.agencyId,
                this.props.sr.referrerIndividualId,
                // allows us to keep the user details in each section, but only save one
                this.state.sourceType == "individual"
                        ? this.state.individualId
                        : this.state.professionalId
        );

    }

    public static emitChanges(commandQueue: CommandQueue,
                              serviceRecipientId: number,
                              taskHandle: string,
                              sourceType: SourceType, selfReferralFrom: boolean,
                              referrerAgencyIdFrom: number, referrerAgencyIdTo: number,
                              referrerIndividualIdFrom: number, referrerIndividualIdTo: number) {

        const cmd = new EditReferralSourceCommand(serviceRecipientId, taskHandle);

        if (sourceType == "selfReferral") {
            cmd.changeSelfReferral(selfReferralFrom, true);
        } else if (sourceType == "individual") {
            cmd.changeAgency(referrerAgencyIdFrom, null);
            cmd.changeReferrer(referrerIndividualIdFrom, referrerIndividualIdTo);
        } else {
            cmd.changeAgency(referrerAgencyIdFrom, referrerAgencyIdTo);
            cmd.changeReferrer(referrerIndividualIdFrom, referrerIndividualIdTo);
        }

        if (cmd.hasChanges()) {
            commandQueue.addCommand(cmd);
        }

    }

    valid(): boolean {
        const tmp: Partial<ReferralDto> = {};
        tmp.referrerAgencyId = this.state.agencyId;
        tmp.referrerIndividualId = this.state.individualId;
        const errors = NewReferralWizard.validateSource(tmp, this.state.sourceType);
        return Object.keys(errors).length == 0;
    }

    render() {
        return possiblyModalForm(
            "", // TODO: this.props.task.title,
            true, true,
            () => this.props.commandForm.cancelForm(),
            () => this.props.commandForm.submitForm().then(this.props.afterSave),
            !this.valid(),
            false,
            <AssociatedContactWizard
                sessionData={this.props.sessionData}
                serviceRecipientId={this.props.sr.serviceRecipientId}
                title={"source of referral"}
                showSelf={true}
                sourceType={this.state.sourceType}
                agencyId={this.state.agencyId}
                professionalId={this.state.professionalId}
                individualId={this.state.individualId}
                onSourceTypeChange={(sourceType: SourceType) => this.setState({sourceType})}
                onAgencyProfessionalChange={(agency: Agency, individual: Individual) => this.setState({
                    agencyId: agency?.contactId,
                    professionalId: individual?.contactId
                })}
                onIndividualChange={(individual: Individual) => this.setState({individualId: individual.contactId})}
        />);
    }
}
