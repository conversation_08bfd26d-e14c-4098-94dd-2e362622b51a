import $ = require("jquery");

import * as applicationProperties from "application-properties";
import BaseControl = require("./BaseControl");

class Spinner extends BaseControl {
    constructor() {
        super($("<img>")
                .addClass("spinner")
                .attr("src", applicationProperties.resourceRootPath + "themes/ecco/images/loading.gif")
                .attr("width", "16")
                .attr("height", "16"));
    }
}

export = Spinner;