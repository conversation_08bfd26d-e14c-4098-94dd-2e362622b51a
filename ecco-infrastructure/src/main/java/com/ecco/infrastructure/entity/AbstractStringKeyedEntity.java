package com.ecco.infrastructure.entity;

import javax.persistence.*;

/**
 *
 */
@MappedSuperclass
public abstract class AbstractStringKeyedEntity extends AbstractUnidentifiedVersionedEntity<String> {

    private static final long serialVersionUID = 1L;

    /** e.g. rfrl:1234 */
    @Id
    @Column(name="id", nullable=false) // oracle doesn't like using unique=true
    private String id = null;

    public AbstractStringKeyedEntity() {
    }

    protected AbstractStringKeyedEntity(String id) {
        this.id = id;
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public void setId(String id) {
        this.id = id;
    }
}
