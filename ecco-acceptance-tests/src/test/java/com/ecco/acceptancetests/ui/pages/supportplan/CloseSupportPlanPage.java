package com.ecco.acceptancetests.ui.pages.supportplan;

import org.openqa.selenium.WebDriver;

public class CloseSupportPlanPage extends SupportPlanBasePage {

    private static final String LINK_TEXT_CANCEL = "cancel";

    public static final String REASON_MOVED_TO_LA_HA = "moved to LA/HA";
    public static final String REASON_ALTERNATIVE_ACCOM = "found own alternative accommodation";
    public static final String REASON_MOVED_FAMILY = "moved to live with family/friends";
    public static final String REASON_LEFT = "moved left when asked/given notice";
    public static final String REASON_EVICTED = "evicted through court proceedings";
    public static final String REASON_DISAPPEARED = "disappeared";

    public CloseSupportPlanPage(WebDriver webDriver) {
        super(webDriver);
    }

    public void cancel() {
        clickLink(LINK_TEXT_CANCEL);
        // TODO which page next?
    }

    /**
     * Select a reason and close the support plan - use one of the REASON_ constants in this class as input
     */
    public CloseSupportPlanConfirmationPage close(String reason) {
        clickLink(reason);
        return new CloseSupportPlanConfirmationPage(getWebDriver());
    }
}
