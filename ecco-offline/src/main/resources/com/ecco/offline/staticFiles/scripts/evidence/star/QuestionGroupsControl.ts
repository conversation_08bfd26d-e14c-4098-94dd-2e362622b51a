import $ = require("jquery");
import Lazy = require("lazy");
import EccoElement = require("../../controls/Element");
import GraphContext = require("../../evidence/graph/GraphContext");
import ServiceRecipientNodeWrapper = require("../../evidence/star/ServiceRecipientNodeWrapper");
import BaseEvidenceControl = require("../BaseEvidenceControl");
import Sequence = LazyJS.Sequence;
import {delay, EccoDate, EccoDateTime, ResizeEvent} from "@eccosolutions/ecco-common";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {CommandAjaxRepository, CommandQueue, GoalUpdateCommand, QuestionAnswerCommand} from "ecco-commands";
import {apiClient} from "ecco-components";
import {
    ConfigResolver,
    QuestionnaireWorkAjaxRepository,
    QuestionnaireWorkDto,
    ServiceRecipientWithEntities,
    SessionData
} from "ecco-dto";
import {BaseServiceRecipientCommandDto, QuestionAnswerCommandDto} from "ecco-dto/evidence-dto";
import {QuestionGroup} from "ecco-dto/service-config-dto";
import {EvidenceDef} from "ecco-evidence";
import {OutcomeStarTreeControl} from "../../draw/outcomeStar-tree";
import {QuestionAnswerUpdateEvent} from "../../evidence/events";
import {OutcomeStarAnswerIdName, OutcomeStarArmDefData, StarCommandFactory} from "../../evidence/star/domain";
import {getCommandQueueRepository} from "ecco-offline-data";
import {EvidenceControl} from "../evidenceControls";

let commandRepository = new CommandAjaxRepository(apiClient);
let snapshotRepository = new QuestionnaireWorkAjaxRepository(apiClient);


/**
 * Importing reports/analysis/types for these classes broke offline
 * TODO - perhaps put in common/types or clean up reports/analysis/types
 * These are currently duplicated!
 */
interface Group<T> {
    key: string;

    /** Items related to this key. This allows them to be passed to a table or chained chart if the representation
     * of this key (e.g. a pie segment or bar in a bar chart) is clicked.
     */
    elements: Sequence<T>;

    count?: number;
}
function extractPair<T>(group: [string, Array<T>]): Group<T> {
    if (!group[1]) {
        throw new Error("no elements for group:" + group[0]);
    }
    return {key: group[0], elements: Lazy(group[1])};
}
//

/**
 * Records a click on the star arm, and triggers an event which gets picked up in the base class
 * to call the updateQuestionAnswer method to make visual changes
 */
class QuestionAnswerStarCommandFactory implements StarCommandFactory {

    constructor(private sessionData: SessionData,
                private context: GraphContext,
                private getWorkUuid: () => Uuid,
                private commandQueue: CommandQueue) {
    }

    public create(armId: number, answerId: number): void {
        // BaseUpdateCommand.ADD to mean we are adding a data point (UPDATE would be for editing history?)
        let cmd = new QuestionAnswerCommand("add", this.getWorkUuid(), this.context.serviceRecipientId,
            armId, this.context.evidenceDef.getEvidenceGroup().name, this.context.evidenceDef.getTaskName());
        let answerChoice = this.sessionData.getQuestionById(armId).choices.filter(choice => choice.id == answerId).pop();
        // QuestionnaireEvidenceHandler requires this to be the value of the answer, not id or displayValue
        cmd.changeAnswer(null, answerChoice.value);
        // TODO some hasChanged logic - or merging?
        this.commandQueue.addCommand(cmd);
        QuestionAnswerUpdateEvent.bus.fire(new QuestionAnswerUpdateEvent(cmd, cmd.toDto()));
    }

}

/**
 * Handles the previous data, in terms of showing differing answers over time
 */
class ProgressControl implements EccoElement {

    private $container = $("<div>");
    private $playButton: $.JQuery;
    private $dateDisplay = $("<span>").css("color", "#33aa33");
    private paused = false;
    private iconPlay = "glyphicon glyphicon-play-circle";
    private iconPause = "glyphicon glyphicon-pause";

    constructor(private loader: QuestionAnswerLoader) {

        this.$playButton = $("<a>").addClass(this.iconPlay)
            .css("font-size", "2em")
            .css("text-decoration", "none");
        this.$playButton.click(event => this.playProgress());

        this.$container
            .css("text-align", "center")
            .append(this.$playButton)
            .append($("<br>"))
            .append(this.$dateDisplay)
            .hide();

        if (loader.getBatchTotals() > 0) {
            this.$container.show();
        }
    }

    public updateLabel(label: string) {
        this.$dateDisplay.text(label);
    }

    public element(): $.JQuery {
        return this.$container;
    }

    private playProgress() {
        this.$playButton.removeClass().addClass(this.iconPause)
            .click(event => this.paused = true);

        let delayMs = 2000;
        let handleItem = (i: number): Promise<void> => {
            if (i < this.loader.getBatchTotals()) {
                this.loader.drawBatchProgress(i);
                return delay(delayMs)
                    .then(() => {
                        if (!this.paused) {
                            handleItem(i + 1);
                        } else {
                            this.$playButton.removeClass().addClass(this.iconPlay)
                                .click(event => {
                                    this.paused = false;
                                    handleItem(i + 1);
                                });
                        }
                    });
            } else {
                this.$playButton.removeClass().addClass(this.iconPlay)
                    .click(event => {
                        this.paused = false;
                        handleItem(i + 1);
                    });
                return Promise.resolve(null);
            }
        };
        handleItem(0);
    }

}


/**
 * Loads the data from commands to get the current state, and mark progress
 */
interface QuestionAnswerLoader {
    getBatchTotals(): number;
    drawBatchProgress(index: number);
}

class GroupedCommands {
    constructor(public week: EccoDate, public cmds: QuestionAnswerCommandDto[]) {
    }
}

/**
 * Loads the data from commands to get the current state, and mark progress
 */
class QuestionAnswerCommandLoader implements QuestionAnswerLoader {

    private cmdDtos: QuestionAnswerCommandDto[] = [];
    private loadedCommandsByWeek: Array<GroupedCommands> = [];

    constructor(private serviceRecipientId: number, private evidenceDef: EvidenceDef, private questionDefIds: number[],
                private drawProgress: (label: string, questionDefId: number, answer: string) => void) {}

    public load(): Promise<any> {
        // opt for all commands in the evidencegroup, rather than a snapshot for now
        return commandRepository.findCommandsByServiceRecipientIdAndEvidenceGroup(this.serviceRecipientId, this.evidenceDef.getEvidenceGroup())
            .then((allCmdDtos: BaseServiceRecipientCommandDto[]) => {

                // get questionnaire commands only
                // would be nice as a on-liner, but tsc complains
                allCmdDtos.forEach((cmd) => {
                    if (QuestionAnswerCommandLoader.isQuestionAnswerCommandDto(cmd)) {
                        let qnId = cmd.questionDefId;
                        // if relevant for the currently configured questions
                        if (this.questionDefIds.indexOf(qnId) > -1) {
                            this.cmdDtos.push(cmd);
                        }
                    }
                });

            })
            .then(() => this.batchData());
    }

    public forEachWithCallback(callback: (questionDefId: number, answer: string) => void) {
        this.cmdDtos.forEach(cmdDto =>
            callback(cmdDto.questionDefId, cmdDto.answerChange && cmdDto.answerChange.to));
    }

    public getBatchTotals() {
        return this.loadedCommandsByWeek.length;
    }

    public drawBatchProgress(index: number) {
        let groupedCmds = this.loadedCommandsByWeek[index];
        groupedCmds.cmds.forEach((cmd) => {
            this.drawProgress("w/c " + groupedCmds.week.formatPretty(), cmd.questionDefId, cmd.answerChange.to);
        });
    }

    private batchData() {
        if (this.cmdDtos.length > 0) {
            let firstCommandCreated: EccoDate = EccoDateTime.parseIso8601Utc(this.cmdDtos[0].timestamp).toEccoDate();
            let firstWeekCommencing = firstCommandCreated.subtractDays(firstCommandCreated.getDayOfWeek()-1); // got for Monday
            let cmdDtosSeq : LazyJS.Sequence<QuestionAnswerCommandDto> = Lazy(this.cmdDtos);
            this.loadedCommandsByWeek = this.groupByWeek(firstWeekCommencing, cmdDtosSeq)
                .map((pair: Group<QuestionAnswerCommandDto>) => {
                    let weekCommencingDate = firstWeekCommencing.addDays(parseInt(pair.key) * 7);
                    return new GroupedCommands(weekCommencingDate, pair.elements.toArray());
                }).toArray();
        }
    }
    private groupByWeek(firstWeekCommencing: EccoDate, input: Sequence<QuestionAnswerCommandDto>): Sequence<Group<QuestionAnswerCommandDto>> {
        return input
            .groupBy((inputElement) =>
                QuestionAnswerCommandLoader.getQuantisedWeek(firstWeekCommencing, inputElement)
            )
            .pairs()
            .map( extractPair );
    }
    static getQuantisedWeek(firstWeekCommencing: EccoDate, item: QuestionAnswerCommandDto): string {
        let createdMs = EccoDateTime.parseIso8601Utc(item.timestamp).toUtcJsDate().getUTCMilliseconds();
        let firstWeekCommencingMs = firstWeekCommencing.toUtcJsDate().getUTCMilliseconds();
        let daysBetween = (createdMs - firstWeekCommencingMs) / (1000 * 60 * 60 * 24);
        return Math.floor(daysBetween / 7).toString();
    }
    static isQuestionAnswerCommandDto(cmd: BaseServiceRecipientCommandDto): cmd is QuestionAnswerCommandDto {
        return (<QuestionAnswerCommandDto>cmd).answerChange !== undefined;
    }

}

class GroupedSnapshots {
    constructor(public week: EccoDate, public sshots: QuestionnaireWorkDto[]) {
    }
}

/**
 * Loads the data from snapshot, and mark progress
 */
class QuestionAnswerSnapshotLoader implements QuestionAnswerLoader {

    private snapshotDtos: QuestionnaireWorkDto[] = [];
    private loadedDataByWeek: Array<GroupedSnapshots> = [];

    constructor(private serviceRecipientId: number, private evidenceDef: EvidenceDef, private questionDefIds: number[],
                private drawProgress: (label: string, questionDefId: number, answer: string) => void) {}

    public load(): Promise<any> {
        return snapshotRepository.findQuestionnaireWorkByServiceRecipientId(this.serviceRecipientId, this.evidenceDef.getEvidenceGroup())
            // REVERSE because the ajax orders by workDate desc, created desc
            .then((snapshots) => this.snapshotDtos = snapshots.reverse())
            .then(() => this.batchData());
    }

    public forEachWithCallback(callback: (questionDefId: number, answer: string) => void) {
        this.snapshotDtos.forEach(snapshotDto =>
            snapshotDto.answers.forEach(answer => callback(answer.questionId, answer.answer))
        );
    }

    public getBatchTotals() {
        return this.loadedDataByWeek.length;
    }

    public drawBatchProgress(index: number) {
        let groupedData = this.loadedDataByWeek[index];
        groupedData.sshots.forEach((sshot) => {
            sshot.answers.forEach(answer => this.drawProgress("w/c " + groupedData.week.formatPretty(), answer.questionId, answer.answer));
        });
    }

    private batchData() {
        if (this.snapshotDtos.length > 0) {
            let firstCreated: EccoDate = EccoDateTime.parseIso8601Utc(this.snapshotDtos[0].workDate).toEccoDate();
            let firstWeekCommencing = firstCreated.subtractDays(firstCreated.getDayOfWeek()-1); // got for Monday
            let snapshotDtosSeq : LazyJS.Sequence<QuestionnaireWorkDto> = Lazy(this.snapshotDtos);
            this.loadedDataByWeek = this.groupByWeek(firstWeekCommencing, snapshotDtosSeq)
                .map((pair: Group<QuestionnaireWorkDto>) => {
                    let weekCommencingDate = firstWeekCommencing.addDays(parseInt(pair.key) * 7);
                    return new GroupedSnapshots(weekCommencingDate, pair.elements.toArray());
                }).toArray();
        }
    }
    private groupByWeek(firstWeekCommencing: EccoDate, input: Sequence<QuestionnaireWorkDto>): Sequence<Group<QuestionnaireWorkDto>> {
        return input
            .groupBy((inputElement) =>
                QuestionAnswerSnapshotLoader.getQuantisedWeek(firstWeekCommencing, inputElement)
            )
            .pairs()
            .map( extractPair );
    }
    static getQuantisedWeek(firstWeekCommencing: EccoDate, item: QuestionnaireWorkDto): string {
        let createdMs = EccoDateTime.parseIso8601(item.workDate).toUtcJsDate().getTime();
        let firstWeekCommencingMs = firstWeekCommencing.toUtcJsDate().getTime();
        let daysBetween = (createdMs - firstWeekCommencingMs) / (1000 * 60 * 60 * 24);
        return Math.floor(daysBetween / 7).toString();
    }

}

/**
 * Visual representation of a container of QuestionGroups. This can match the content of the
 * tabs on a questionnaire.
 */
class QuestionGroupsControl extends BaseEvidenceControl implements EvidenceControl {

    private treeControl: OutcomeStarTreeControl;
    private progressControl: ProgressControl;
    private rootNode: ServiceRecipientNodeWrapper;
    private $container = $("<div>");
    private commandQueue = new CommandQueue(getCommandQueueRepository());
    private sessionData: SessionData;
    private configResolver: ConfigResolver;

    constructor(private serviceRecipientWithEntities: ServiceRecipientWithEntities,
                private getWorkUuid: () => Uuid,
                private evidenceDef: EvidenceDef) {
        super();

        this.sessionData = serviceRecipientWithEntities.features;
        this.configResolver = serviceRecipientWithEntities.configResolver;
        let serviceRecipientId = serviceRecipientWithEntities.serviceRecipientId;

        let useOfficialOutcomeStar = this.configResolver.getServiceType().taskDefinitionSettingHasFlag(this.evidenceDef.getTaskName(),
             "showEvidenceStyleAs", "officialOutcomeStar");

        this.treeControl = new OutcomeStarTreeControl(850, 850, useOfficialOutcomeStar); // NOTE: Make these numbers bigger if you increase label size
        this.$container.append(this.treeControl.element());

        let showAnswersAsValue = this.configResolver.getServiceType().taskDefinitionSettingHasFlag(this.evidenceDef.getTaskName(),
             "showStarAnswersAs", "value");
        let questionGroups = this.configResolver.getQuestionGroupsFilteredForTask(this.evidenceDef.getTaskName());
        let drawProgress = (progressLabel: string, questionDefId: number, answer: string) => {
            this.progressControl.updateLabel(progressLabel);
            this.progressQuestionAnswer(questionDefId, answer);
        };
        let arms = QuestionGroupsControl.createStarArms(showAnswersAsValue, questionGroups);
        let questionDefIds = arms.map(arm => arm.id);
        let qaLoader = new QuestionAnswerSnapshotLoader(serviceRecipientWithEntities.serviceRecipientId, this.evidenceDef, questionDefIds, drawProgress);
        qaLoader.load().then(() => {

            // let $reloadBtn = $("<a>").addClass("fa fa-refresh").click( event => this.treeControl.redraw() ); // redrawSlowly
            // this.$container
            // //            .append( $("<a>").addClass("fa fa-pause").click( event => this.pauseResume() ) )
            //     .append($("<div>").append($reloadBtn));

            // create container
            // create structure of arms etc
            this.treeControl.element().appendTo(this.$container);

            // subscribe to events before we setContext, which triggers an event
            this.subscribeToEvents();

            let context = new GraphContext(serviceRecipientId, serviceRecipientWithEntities, null, this.treeControl,
                evidenceDef, null, this.sessionData, this.configResolver);
            let commandFactory = new QuestionAnswerStarCommandFactory(this.sessionData, context, this.getWorkUuid, this.commandQueue);
            this.rootNode = new ServiceRecipientNodeWrapper(serviceRecipientWithEntities, context, commandFactory);
            this.treeControl.setContextNode(this.rootNode.getNode());

            this.drawStar(arms);

            // we could fire events, but no real reason to add the diversion when a direct call will do
            qaLoader.forEachWithCallback((questionDefId: number, answer: string) =>
                this.updateQuestionAnswer(questionDefId, answer)
            );

            // could start with a default message if loaded data length == 0

            ResizeEvent.bus.fire(new ResizeEvent());

            this.progressControl = new ProgressControl(qaLoader);

            this.progressControl.element().prependTo(this.$container);
        });
    }

    // @Override to pick up on the EvidenceUpdateEvent event firing after the data load
/*
    protected subscribeToEvents() {
        // subscribe to normal events
        super.subscribeToEvents();
        // subscribe to initial data event
        EvidenceUpdateEvent.bus(this.evidenceDef.getEvidenceGroupName()).addHandler(
            (event: EvidenceUpdateEvent) => {
                console.info("questionnaire: got event with dto: %o", event.commandDto);
                this.applyInitialDtoCommand(event.commandDto);
            });
    }
*/

    private drawStar(arms: OutcomeStarArmDefData[]) {

        arms.forEach(arm =>
            this.rootNode.addOutcomeStarArm(arm)
        );

        /*
        config:
        family - early years
        physical health
        1:01;2:02;3:03;4:04;5:05;6:06;7:07;8:08;9:09;10:10

        emotional health
        1:01;2:02;3:03;4:04;5:05;6:06;7:07;8:08;9:09;10:10

        keeping your children safe
        1:01;2:02;3:03;4:04;5:05;6:06;7:07;8:08;9:09;10:10

        social networks
        1:01;2:02;3:03;4:04;5:05;6:06;7:07;8:08;9:09;10:10

        boundaries and routines
        1:01;2:02;3:03;4:04;5:05;6:06;7:07;8:08;9:09;10:10

        child development
        1:01;2:02;3:03;4:04;5:05;6:06;7:07;8:08;9:09;10:10

        home, money and work
        1:01;2:02;3:03;4:04;5:05;6:06;7:07;8:08;9:09;10:10
        */

        // family - early years
        // this.rootNode.addOutcomeStarArm(this.createOutcomeStarArmData(1, "physical health", answers));
        // this.rootNode.addOutcomeStarArm(this.createOutcomeStarArmData(2, "emotional wellbeing", answers));
        // this.rootNode.addOutcomeStarArm(this.createOutcomeStarArmData(3, "keeping your children safe", answers));
        // this.rootNode.addOutcomeStarArm(this.createOutcomeStarArmData(4, "social networks", answers));
        // this.rootNode.addOutcomeStarArm(this.createOutcomeStarArmData(5, "boundaries and routines", answers));
        // this.rootNode.addOutcomeStarArm(this.createOutcomeStarArmData(6, "child development", answers));
        // this.rootNode.addOutcomeStarArm(this.createOutcomeStarArmData(7, "home, money and work", answers));

        // recovery star
        // this.rootNode.addOutcomeStarArm(this.createOutcomeStarArmData(1, "managing mental health", answers));
        // this.rootNode.addOutcomeStarArm(this.createOutcomeStarArmData(2, "physical health & self-care", answers));
        // this.rootNode.addOutcomeStarArm(this.createOutcomeStarArmData(3, "living skills", answers));
        // this.rootNode.addOutcomeStarArm(this.createOutcomeStarArmData(4, "social networks", answers));
        // this.rootNode.addOutcomeStarArm(this.createOutcomeStarArmData(5, "work", answers));
        // this.rootNode.addOutcomeStarArm(this.createOutcomeStarArmData(6, "relationships", answers));
        // this.rootNode.addOutcomeStarArm(this.createOutcomeStarArmData(7, "additive behaviour", answers));
        // this.rootNode.addOutcomeStarArm(this.createOutcomeStarArmData(8, "responsibilities", answers));
        // this.rootNode.addOutcomeStarArm(this.createOutcomeStarArmData(9, "identity & self-esteem", answers));
        // this.rootNode.addOutcomeStarArm(this.createOutcomeStarArmData(10, "trust & hope", answers));
    }

    static createStarArms(showAnswersAsValue: boolean, questionGroups: Array<QuestionGroup>): OutcomeStarArmDefData[] {
        if (questionGroups.length == 1) {
            return this.createStarFromOneQuestionGroup(showAnswersAsValue, questionGroups); // one group with many questions
        } else {
            // find the servicetype, find the task, find the questionGroups
            return this.createStarFromManyQuestionGroups(showAnswersAsValue, questionGroups); // many groups each with one question
        }
    }

    static createStarFromOneQuestionGroup(showAnswersAsValue: boolean, questionGroups: Array<QuestionGroup>): OutcomeStarArmDefData[] {
        // find the servicetype, find the task, find the questionGroups
        let questions = questionGroups.pop().questions;

        let arms: OutcomeStarArmDefData[] = [];
        questions.forEach(question => {
            let answers = question.choices.map(choice => new OutcomeStarAnswerIdName(choice.id,
                showAnswersAsValue ? choice.value : choice.displayValue,
                choice.value));
            arms.push(this.createOutcomeStarArmData(question.id, question.name, answers));
        });
        return arms;
    }

    static createStarFromManyQuestionGroups(showAnswersAsValue: boolean, questionGroups: Array<QuestionGroup>): OutcomeStarArmDefData[] {
        let arms: OutcomeStarArmDefData[] = [];
        questionGroups.forEach(questionGroup => {
            questionGroup.questions.forEach(question => {
                if (!question.disabled) {
                    let answers = question.choices.map(choice => new OutcomeStarAnswerIdName(choice.id,
                                                                                             showAnswersAsValue ? choice.value : choice.displayValue,
                                                                                             choice.value));
                    let armLabel = '[' + questionGroup.name + '] ' + question.name;
                    arms.push(this.createOutcomeStarArmData(question.id, armLabel, answers));
                }
            });
        });
        return arms;
    }

    static createOutcomeStarArmData(id: number, name: string, displayAnswers: OutcomeStarAnswerIdName[]) {
        let outcomeStarArmDefData = new OutcomeStarArmDefData();
        outcomeStarArmDefData.id = id;
        outcomeStarArmDefData.name = name;
        outcomeStarArmDefData.visualAnswers = displayAnswers;
        return outcomeStarArmDefData;
    }

    // @Override
    public updateAction(goalUpdateCommand: GoalUpdateCommand): void {
    }

    /**
     * Triggered by an QuestionAnswerUpdateEvent from scoreClicked's command create in OutcomeStarScoreNodeWrapper.
     * QuestionGroupsControl subscribes and calls updateQuestionAnswer
     * which only needs to update the visuals, since the command has already been generated.
     * Triggered also by the initial loaded data in QuestionGroupsControl (so doesn't generate cmds).
     * The trigger is only sent down the relevant arm of the update.
     */
    // @Override
    public updateQuestionAnswer(questionDefId: number, answer: string): void {
        let question = this.sessionData.getQuestionById(questionDefId);
        if (question) {
            // update the nodes affected by the change in score
            this.rootNode.updateArmAnswerVisuals(question.id, answer);
            // redraw again to cater for the spider-web which don't have a backing-node
            this.rootNode.getNode().redraw();
        }
    }

    // @Override
    private progressQuestionAnswer(questionDefId: number, answer: string) {
        let question = this.sessionData.getQuestionById(questionDefId);
        if (question) {
            this.rootNode.progressArmAnswerVisuals(question.id, answer);
            this.rootNode.getNode().redraw();
        }
    }

    /** True if required fields are set */
    public isValid(): boolean {
        return this.treeControl.isValid();
    }

    public emitChangesTo(queue: CommandQueue) {
        queue.addQueue(this.commandQueue);
    }

    public element(): $.JQuery {
        return this.$container;
    }
}
export = QuestionGroupsControl;
