import {Credentials, UserAjaxRepository} from "ecco-dto";
import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON>alog,
    DialogContent,
    DialogTitle,
    Grid,
    TextField,
    useMediaQuery
} from "@eccosolutions/ecco-mui";
import * as React from "react";
import {FC, useState} from "react";
import * as ReactDOM from "react-dom";
import {GeekyBlokeSays} from "../user/LoggedOut";
import {EccoTheme} from "../theme";
import {AzureLoginProvider} from "./AzureLoginProvider";
import {usePromise} from "../data/entityLoadHooks";
import {apiClient} from "../environment";
import {useId} from "@reach/auto-id";

const {isLoginProvidersOnly} = window.applicationProperties;

interface Props {
    onSubmit: (credentials: Credentials) => void;
}

function useLoginError(hasLoginError: boolean) {
    const userRepository = new UserAjaxRepository(apiClient)
    const {resolved, error, loading} = usePromise(
            () => hasLoginError
                    ? userRepository.findLastSessionLoginError()
                    : Promise.resolve(null)
            ,[hasLoginError]);
    return {lastLoginErrorMessage: resolved?.login_error, error, loading};
}

export const LoginForm: FC<Props> = ({onSubmit}) => {
    const [username, setUsername] = useState("");
    const [password, setPassword] = useState("");

    const params = new URLSearchParams(window.location.search);
    const {lastLoginErrorMessage} = useLoginError(params.has("login_error"));
    const LoginError = () =>
            lastLoginErrorMessage
                // see content/login.jsp <spring:message code="checkedErrorUnhandled.message"/>
                ? <Alert severity="warning">{lastLoginErrorMessage}</Alert>
                : null

    const LoginUsernamePassword = (
        <>
            <Grid item xs={12} sm={10}>
                <LoginError />
            </Grid>
            <Grid item xs={12} sm={10}>
                <TextField
                    id={useId()}
                    InputLabelProps={{shrink: true}}
                    fullWidth
                    label="username"
                    name="username"
                    autoFocus
                    autoComplete="username"
                    onChange={event => setUsername(event.target.value)}
                />
            </Grid>
            <Grid item xs={12} sm={10}>
                <TextField
                    id={useId()}
                    InputLabelProps={{shrink: true}}
                    fullWidth
                    name="password"
                    label="password"
                    type="password"
                    autoComplete="current-password"
                    onChange={event => setPassword(event.target.value)}
                />
            </Grid>
            <Grid item xs={12} style={{textAlign: "right"}}>
                <Button onClick={() => onSubmit({username, password})} color="primary">
                    login
                </Button>
            </Grid>
        </>
    );

    return (
        <form
            onSubmit={e => e.preventDefault()}
            onKeyUp={e => {
                const ENTER = 13;
                if (e.keyCode === ENTER) {
                    onSubmit({username, password});
                    //props.onClose();
                }
            }}
        >
            {/* Try to be good as far as password managers go: https://www.chromium.org/developers/design-documents/create-amazing-password-forms */}
            <Grid container justify="center" spacing={2}>
                <AzureLoginProvider />
                {!isLoginProvidersOnly && LoginUsernamePassword}
            </Grid>
        </form>
    );
}

export const LoginDialog: FC<Props> = ({onSubmit}) => {
    const showWelcome = useMediaQuery("(min-width: 768px)");

    return (
        <Dialog
            className="m-ui"
            open={true}
            aria-labelledby="dialog-title"
            // aria-describedby="dialog-description"
        >
            <DialogTitle
                hidden={showWelcome} // for aria only
                id="dialog-title"
            >
                login
            </DialogTitle>
            <DialogContent>
                {showWelcome && (
                    <div style={{zoom: 0.85, marginLeft: 24}}>
                        <GeekyBlokeSays>
                            <div style={{marginBottom: 12, fontWeight: "bold"}}>
                                Let's get you signed in.
                            </div>
                            <small>
                                And, thanks in
                                <br />
                                advance for
                                <br />
                                everything
                                <br />
                                you do.
                            </small>
                        </GeekyBlokeSays>
                    </div>
                )}
                <LoginForm onSubmit={onSubmit} />
            </DialogContent>
        </Dialog>
    );
};

const mountPoint = document.createElement("div");

export function askUserForCredentials(): Promise<Credentials> {
    return new Promise<Credentials>(resolve => {
        const loginForm = (
            <EccoTheme prefix="login">
                <LoginDialog
                    onSubmit={credentials => {
                        resolve(credentials);
                        ReactDOM.unmountComponentAtNode(mountPoint);
                    }}
                />
            </EccoTheme>
        );
        // we don't want any buttons, but to achieve that we need to be DialogContent adapter
        ReactDOM.render(loginForm, mountPoint);
    });
}
