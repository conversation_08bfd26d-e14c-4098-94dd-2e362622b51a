package com.ecco.data.client.dataimport.csv;

import javax.annotation.Nullable;

import org.springframework.beans.BeanWrapper;
import org.springframework.beans.ConfigurablePropertyAccessor;
import org.springframework.beans.PropertyAccessorFactory;

import com.google.common.base.Throwables;

public class BeanWrapperInstance<T> implements Instance<T> {

    private final ConfigurablePropertyAccessor wrapper;
    private final T instance;

    BeanWrapperInstance(Class<T> type) {
        T newInstance = null;
        try {
            newInstance = type.newInstance();
        } catch (Exception e) {
            Throwables.propagate(e);
        }
        this.instance = newInstance;
        this.wrapper = PropertyAccessorFactory.forBeanPropertyAccess(instance);
        wrapper.setConversionService(DataImportConversionService.getInstance());
        ((BeanWrapper) wrapper).setAutoGrowNestedPaths(true);
    }

    T getResult() {
        return instance;
    }

    @Override
    public void write(String propertyPath, @Nullable Object value) {
        // useful breakpoint in setting actual value
        wrapper.setPropertyValue(propertyPath, value);
    }
}
