<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <changeSet id="DEV-1326-st-referralaspects-contracts2" author="adamjhamer" context="!test-data">
        <preConditions onFail="MARK_RAN">
            <not>
                <changeSetExecuted id="DEV-1326-st-referralaspects-contracts" author="adamjhamer" changeLogFile="classpath:sql/1.2-changes/finance-domain/001-financeDomainChangeLog.xml"/>
            </not>
        </preConditions>
        <insert tableName="servicetypes">
            <column name="id" valueNumeric="-300"/>
            <column name="version" valueNumeric="0"/>
            <column name="accommodation" valueBoolean="false"/>
            <column name="anonymousSupport" valueBoolean="false"/>
            <column name="contractRequired" valueBoolean="false"/>
            <column name="logtime" valueBoolean="false"/>
            <column name="multipleReferrals" valueBoolean="false"/>
            <column name="name" value="contracts-default"/>
        </insert>
        <insert tableName="services">
            <column name="id" valueNumeric="-300"/>
            <column name="version" valueNumeric="0"/>
            <column name="name" value="contracts"/>
            <column name="servicetypeid" valueNumeric="-300"/>
        </insert>
        <insert tableName="services_projects">
            <column name="id" valueNumeric="-300"/>
            <column name="serviceId" valueNumeric="-300"/>
        </insert>
        <insert tableName="servicetypes_referralaspects">
            <column name="allowNext" valueBoolean="false"/>
            <column name="orderby" valueNumeric="0"/>
            <column name="version" valueNumeric="0"/>
            <column name="referralaspectId" valueNumeric="45"/> <!-- clientWithContact => contract -->
            <column name="servicetypeId" valueNumeric="-300"/>
        </insert>
        <insert tableName="servicetypes_referralaspects">
            <column name="allowNext" valueBoolean="false"/>
            <column name="orderby" valueNumeric="1"/>
            <column name="version" valueNumeric="0"/>
            <column name="referralaspectId" valueNumeric="15"/> <!-- from / sourceWithIndividual -->
            <column name="servicetypeId" valueNumeric="-300"/>
        </insert>
        <insert tableName="servicetypes_referralaspects">
            <column name="allowNext" valueBoolean="false"/>
            <column name="orderby" valueNumeric="2"/>
            <column name="version" valueNumeric="0"/>
            <column name="referralaspectId" valueNumeric="18"/>  <!-- referralView -->
            <column name="servicetypeId" valueNumeric="-300"/>
        </insert>
        <insert tableName="servicetypes_referralaspects">
            <column name="allowNext" valueBoolean="false"/>
            <column name="orderby" valueNumeric="50"/>
            <column name="version" valueNumeric="0"/>
            <column name="referralaspectId" valueNumeric="50"/>  <!-- close -->
            <column name="servicetypeId" valueNumeric="-300"/>
        </insert>
        <insert tableName="servicetypes_referralaspects">
            <column name="allowNext" valueBoolean="false"/>
            <column name="orderby" valueNumeric="99"/>
            <column name="version" valueNumeric="0"/>
            <column name="referralaspectId" valueNumeric="11"/>  <!-- endFlow -->
            <column name="servicetypeId" valueNumeric="-300"/>
        </insert>
    </changeSet>

</databaseChangeLog>
