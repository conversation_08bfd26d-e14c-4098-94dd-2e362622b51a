import {ApiClient} from "ecco-dto";
import {AclEntriesDto, AclEntryDto} from "ecco-dto/acl-dto";
import {AclRepository} from "./AclRepository";

class AclWrapper implements AclEntriesDto {
    constructor(public acls: AclEntryDto[], public usernamesToClear: string[]) { }
}

export class AclAjaxRepository implements AclRepository {
    constructor(private apiClient: ApiClient) {
    }

    public ensureAcls(): Promise<void> {
        return this.apiClient.post<void>("acls/autoAcls/", null);
    }

    public ensureAclEntries(): Promise<void> {
        return this.apiClient.post<void>("acls/autoAclEntries/", null);
    }

    public listEntries(): Promise<AclEntryDto[]> {
        return this.apiClient.get<AclEntryDto[]>("acls/entries/");
    }

    public applyEntries(acls: AclEntryDto[], usernamesToClear: string[]): Promise<void> {
        var aclWrapper = new AclWrapper(acls, usernamesToClear);
        return this.apiClient.post<void>("acls/entries/", aclWrapper);
    }

    public findByUsername(username: string): Promise<AclEntryDto[]> {
        return this.apiClient.get<AclEntryDto[]>(`acls/entriesByUser/${username}/`);
    }

}
