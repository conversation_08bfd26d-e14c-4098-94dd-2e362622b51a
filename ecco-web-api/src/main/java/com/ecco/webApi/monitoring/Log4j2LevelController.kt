package com.ecco.webApi.monitoring

import com.ecco.webApi.controllers.BaseWebApiController
import lombok.extern.slf4j.Slf4j
import org.springframework.security.access.annotation.Secured
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RestController
import java.lang.management.ManagementFactory
import java.util.*
import javax.management.Attribute
import javax.management.MBeanServer
import javax.management.ObjectName

@RestController
@Slf4j
class Log4j2LevelController : BaseWebApiController() {

    @GetMapping("/loggers")
    @Secured("ROLE_SYSADMIN")
    fun listLoggers(): List<Pair<String, String>> {
        val mBeanServer = ManagementFactory.getPlatformMBeanServer()
        val firstInstanceName = getFirstLog4JInstance(mBeanServer)

        // With first entry, set the Level attribute to the desired level
        val loggerObjectName = ObjectName("${firstInstanceName.canonicalName},component=Loggers,name=*")
        return mBeanServer.queryNames(loggerObjectName, null).map { logger ->
            val loggerName = logger.keyPropertyList["name"]!!
            val level = mBeanServer.getAttribute(logger, "Level").toString()
            log.info("Logger {} has level {}", loggerName, level)
            Pair(loggerName, level)
        }
    }

    // NB /api/loggers/org.hibernate.SQL/DEBUG/
    @GetMapping("/loggers/{loggerName}/{level}")
    @Secured("ROLE_SYSADMIN")
    fun changeLogLevel(
        @PathVariable("loggerName") loggerName: String,
        @PathVariable("level") level: String
    ) {
        val mBeanServer = ManagementFactory.getPlatformMBeanServer()

        // With first entry, set the Level attribute to the desired level
        val firstInstanceName = getFirstLog4JInstance(mBeanServer)
        val attribute = Attribute("Level", level)
        val loggerObjectName = ObjectName("${firstInstanceName.canonicalName},component=Loggers,name=$loggerName")
        mBeanServer.queryNames(loggerObjectName, null).ifEmpty {
            // If no logger with the given name exists, create it
            // FIXME: This doesn't work - we get The MBean class could not be loaded by the default loader repository
            // mBeanServer.createMBean("org.apache.logging.log4j.core.jmx.LoggerConfigAdmin", loggerObjectName)
            // mBeanServer.queryNames(loggerObjectName, null)
            throw RuntimeException(
                "Logger $loggerName not found - and we can't currently create it." +
                    " Please create it manually first in log4j-prod/dev.xml")
        }
        .first().run {
            mBeanServer.setAttribute(this, attribute)
        }
        log.info("Changed log level of logger {} to {}", loggerName, level)
    }

    private fun getFirstLog4JInstance(mBeanServer: MBeanServer): ObjectName {
        // Expect to at least find one log4j2 logger registry
        val allTypes = ObjectName("org.apache.logging.log4j2:type=*")
        val allInstanceNames = mBeanServer.queryNames(allTypes, null)
        if (allInstanceNames.isEmpty()) {
            throw RuntimeException("No log4j2 logger registry found")
        }
        return allInstanceNames.iterator().next()!!
    }
}