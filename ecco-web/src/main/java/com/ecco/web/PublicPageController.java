package com.ecco.web;

import com.ecco.infrastructure.config.ApplicationProperties;
import com.ecco.security.ReferenceDataImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Controller
@AllArgsConstructor
public class PublicPageController {

    private final ApplicationProperties applicationProperties;
    private final ReferenceDataImpl referenceData;

    @ModelAttribute
    public ApplicationProperties applicationProperties() {
        return applicationProperties;
    }

    @GetMapping("/")
    public void getRoot(HttpServletRequest request, HttpServletResponse response) throws IOException {
            response.sendRedirect(request.getContextPath() + "/nav/r/welcome/");
    }

    @GetMapping("/p/r/refer/**")
    public String getRefer(ModelMap model, HttpServletRequest request) {
        referenceData.addReferenceDataToModel(model, request);
        return "online/refer";
    }

    // this is good for boot <context>/p/r/incident but not war
    @GetMapping("/p/r/incident/**")
    public String getIncident(ModelMap model, HttpServletRequest request) {
        referenceData.addReferenceDataToModel(model, request);
        return "online/incident";
    }

    // this is good for boot <context>/p/r/repair but not war
    @GetMapping("/p/r/repair/**")
    public String getRepair(ModelMap model, HttpServletRequest request) {
        referenceData.addReferenceDataToModel(model, request);
        return "online/repair";
    }

    // this is good for boot but not war
    // <context>/p/r/guidance/...
    @GetMapping("/p/r/guidance/**")
    public String getGuidance(ModelMap model, HttpServletRequest request) {
        referenceData.addReferenceDataToModel(model, request);
        return "online/guidance";
    }

}
