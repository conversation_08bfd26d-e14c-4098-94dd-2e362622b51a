<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd">

    <!-- NOTE: Intention is to cover all the settings here -->


    <changeSet id="ECCO-354-servicetypes" author="neale" context="1.1-base-data">
        <validCheckSum>3:d0f46cbb62b45389153f9a7179d6e918</validCheckSum>
        <insert tableName="servicetypes">
            <column name="id" valueNumeric="97"/>
            <column name="version" valueNumeric="0"/>
            <column name="accommodation" valueBoolean="false"/>
            <column name="anonymousSupport" valueBoolean="false"/>
            <column name="contractRequired" valueBoolean="false"/>
            <column name="logtime" valueBoolean="false"/>
            <column name="multipleReferrals" valueBoolean="false"/>
            <column name="name" value="ypas-counselling"/>
        </insert>
        <insert tableName="services">
            <column name="id" valueNumeric="97"/>
            <column name="version" valueNumeric="0"/>
            <column name="name" value="ypas-counselling-workflow"/>
            <column name="servicetypeid" valueNumeric="97"/>
        </insert>
    </changeSet>
    <changeSet id="ECCO-354-enable-workflow" author="neale" context="1.1-base-data">
        <validCheckSum>3:4c23f3f98dcb81d3eec3f6e69d1a9925</validCheckSum>
        <insert tableName="servicetypes_workflow">
            <column name="id" valueNumeric="97"/>
            <column name="version" valueNumeric="0"/>
            <column name="process_key" value="ypas-counselling"/>
        </insert>
    </changeSet>

    <changeSet id="ECCO-354-st-referralaspects-wizard" author="neale" context="1.1-base-data">
        <insert tableName="servicetypes_referralaspects">
            <column name="allowNext" valueBoolean="false"/>
            <column name="orderby" valueNumeric="0"/>
            <column name="version" valueNumeric="0"/>
            <column name="referralaspectId" valueNumeric="62"/>
            <column name="servicetypeId" valueNumeric="97"/>
        </insert>
        <insert tableName="servicetypes_referralaspects">
            <column name="allowNext" valueBoolean="false"/>
            <column name="orderby" valueNumeric="1"/>
            <column name="version" valueNumeric="0"/>
            <column name="referralaspectId" valueNumeric="63"/>
            <column name="servicetypeId" valueNumeric="97"/>
        </insert>
        <insert tableName="servicetypes_referralaspects">
            <column name="allowNext" valueBoolean="false"/>
            <column name="orderby" valueNumeric="2"/>
            <column name="version" valueNumeric="0"/>
            <column name="referralaspectId" valueNumeric="45"/>
            <column name="servicetypeId" valueNumeric="97"/>
        </insert>
        <insert tableName="servicetypes_referralaspects">
            <column name="allowNext" valueBoolean="false"/>
            <column name="orderby" valueNumeric="3"/>
            <column name="version" valueNumeric="0"/>
            <column name="referralaspectId" valueNumeric="15"/>
            <column name="servicetypeId" valueNumeric="97"/>
        </insert>
        <insert tableName="servicetypes_referralaspects">
            <column name="allowNext" valueBoolean="false"/>
            <column name="orderby" valueNumeric="4"/>
            <column name="version" valueNumeric="0"/>
            <column name="referralaspectId" valueNumeric="18"/>
            <column name="servicetypeId" valueNumeric="97"/>
        </insert>
    </changeSet>
    <changeSet id="ECCO-354-services-projects" author="neale" context="1.1-base-data">
            <insert tableName="services_projects">
            <column name="serviceId" valueNumeric="97"/>
            <column name="projectId" valueNumeric="5"/>
        </insert>
        <insert tableName="services_projects">
            <column name="serviceId" valueNumeric="97"/>
            <column name="projectId" valueNumeric="6"/>
        </insert>
    </changeSet>

    <changeSet id="ECCO-354-servicetypes_questiongroups" author="neale" context="1.1-base-data">
        <insert tableName="servicetypes_questiongroups">
            <column name="servicetypeId" valueNumeric="97"/>
            <column name="questiongroupId" valueNumeric="1"/>
        </insert>
        <insert tableName="servicetypes_questiongroups">
            <column name="servicetypeId" valueNumeric="97"/>
            <column name="questiongroupId" valueNumeric="2"/>
        </insert>
        <insert tableName="servicetypes_questiongroups">
            <column name="servicetypeId" valueNumeric="97"/>
            <column name="questiongroupId" valueNumeric="4"/>
        </insert>
        <insert tableName="servicetypes_questiongroups">
            <column name="servicetypeId" valueNumeric="97"/>
            <column name="questiongroupId" valueNumeric="5"/>
        </insert>
        <insert tableName="servicetypes_questiongroups">
            <column name="servicetypeId" valueNumeric="97"/>
            <column name="questiongroupId" valueNumeric="6"/>
        </insert>
        <insert tableName="servicetypes_questiongroups">
            <column name="servicetypeId" valueNumeric="97"/>
            <column name="questiongroupId" valueNumeric="7"/>
        </insert>
        <insert tableName="servicetypes_questiongroups">
            <column name="servicetypeId" valueNumeric="97"/>
            <column name="questiongroupId" valueNumeric="8"/>
        </insert>
        <insert tableName="servicetypes_questiongroups">
            <column name="servicetypeId" valueNumeric="97"/>
            <column name="questiongroupId" valueNumeric="9"/>
        </insert>
        <insert tableName="servicetypes_questiongroups">
            <column name="servicetypeId" valueNumeric="97"/>
            <column name="questiongroupId" valueNumeric="10"/>
        </insert>
        <insert tableName="servicetypes_questiongroups">
            <column name="servicetypeId" valueNumeric="97"/>
            <column name="questiongroupId" valueNumeric="11"/>
        </insert>
        <insert tableName="servicetypes_questiongroups">
            <column name="servicetypeId" valueNumeric="97"/>
            <column name="questiongroupId" valueNumeric="12"/>
        </insert>
        <insert tableName="servicetypes_questiongroups">
            <column name="servicetypeId" valueNumeric="97"/>
            <column name="questiongroupId" valueNumeric="13"/>
        </insert>
        <insert tableName="servicetypes_questiongroups">
            <column name="servicetypeId" valueNumeric="97"/>
            <column name="questiongroupId" valueNumeric="100"/>
        </insert>
        <insert tableName="servicetypes_questiongroups">
            <column name="servicetypeId" valueNumeric="97"/>
            <column name="questiongroupId" valueNumeric="101"/>
        </insert>
        <insert tableName="servicetypes_questiongroups">
            <column name="servicetypeId" valueNumeric="97"/>
            <column name="questiongroupId" valueNumeric="102"/>
        </insert>
    </changeSet>
</databaseChangeLog>
