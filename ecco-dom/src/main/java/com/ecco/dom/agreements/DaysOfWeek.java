package com.ecco.dom.agreements;

import java.time.DayOfWeek;
import java.util.*;

import javax.annotation.Nullable;
import javax.persistence.Column;
import javax.persistence.Embeddable;

import org.jetbrains.annotations.Contract;
import org.joda.time.DateTimeConstants;

import com.google.common.base.Joiner;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;

/**
 * An integer-based representation for specifying days of the week.
 * This is Sun-based, which is Calendar.SUNDAY based (is Sun = 1) although this is not ISO,
 * but it is consistent with the underlying calendar system which uses ical4j
 * See CosmoCalendarServiceTest getDayList().
 */
@Embeddable
public class DaysOfWeek implements com.ecco.calendar.core.DaysOfWeek, Cloneable {

    private static final long serialVersionUID = 1L;

    public static int SUN = 1; // Matches Calendar.Sunday being day 1 of the week (so bit 0). NB This is not the ISO approach of Mon first.
    public static int MON = 2;
    public static int TUE = 4;
    public static int WED = 8;
    public static int THUR = 16;
    public static int FRI = 32;
    public static int SAT = 64;

    public static DaysOfWeek fromBits(int bits) {
        DaysOfWeek result = new DaysOfWeek();
        for(int calDay = Calendar.SUNDAY; calDay <= Calendar.SATURDAY; calDay++) {
            int mask = 1 << calDay - Calendar.SUNDAY;
            if ((bits & mask) != 0) {
                result.setCalendarDay(calDay, true);
            }
        }
        return result;
    }

    /**
     * Transforms a string array of days into DaysOfWeek.
     * Expects one of "mon", "tues", "wed", "thurs", "fri", "sat", "sun"
     * but ignores order and case.
     */
    public static DaysOfWeek fromStringDays(String[] dow) {
        DaysOfWeek result = new DaysOfWeek();
        if (dow == null) {
            return result;
        }
        // ignore the order of dow, just to be safe
        for (String day : dow) {
            if ("mon".equalsIgnoreCase(day)) {
                result.setMonday(true);
            }
            if ("tues".equalsIgnoreCase(day)) {
                result.setTuesday(true);
            }
            if ("wed".equalsIgnoreCase(day)) {
                result.setWednesday(true);
            }
            if ("thurs".equalsIgnoreCase(day)) {
                result.setThursday(true);
            }
            if ("fri".equalsIgnoreCase(day)) {
                result.setFriday(true);
            }
            if ("sat".equalsIgnoreCase(day)) {
                result.setSaturday(true);
            }
            if ("sun".equalsIgnoreCase(day)) {
                result.setSunday(true);
            }
        }
        return result;
    }

    /**
     * Gets the days corresponding to ISO, where Mon is 1.
     */
    public static DaysOfWeek fromCalendarDayISO(DayOfWeek dow) {
        DaysOfWeek result = new DaysOfWeek();
        result.setCalendarDayFromISO(dow, true);
        return result;
    }

    /**
     * Gets the days corresponding to ISO, where Mon is 1.
     */
    public static DaysOfWeek fromCalendarDaysISO(DayOfWeek... days) {
        DaysOfWeek result = new DaysOfWeek();
        for (DayOfWeek day : days) {
            result.setCalendarDayFromISO(day, true);
        }
        return result;
    }

    /**
     * Gets the days corresponding to ISO, where Mon is 1.
     */
    public static DaysOfWeek fromCalendarDaysISO(List<Integer> days) {
        if (days == null) {
            return null;
        }
        DaysOfWeek result = new DaysOfWeek();
        days.forEach(d -> result.setCalendarDayFromISO(DayOfWeek.of(d), true));
        return result;
    }

    /**
     * Gets the days corresponding to Sun as 1.
     */
    @Contract("null -> null; !null -> !null") // returns null if arg is null
    public static @Nullable DaysOfWeek fromCalendarDays(List<Integer> days) {
        if (days == null) {
            return null;
        }
        DaysOfWeek result = new DaysOfWeek();
        days.forEach(d -> result.setCalendarDay(d, true));
        return result;
    }

    public int daysAttending() {
        DaysOfWeek days = this;
        return (days.isSunday() ? DaysOfWeek.SUN : 0)
                | (days.isMonday() ? DaysOfWeek.MON : 0)
                | (days.isTuesday() ? DaysOfWeek.TUE : 0)
                | (days.isWednesday() ? DaysOfWeek.WED : 0)
                | (days.isThursday() ? DaysOfWeek.THUR : 0)
                | (days.isFriday() ? DaysOfWeek.FRI : 0)
                | (days.isSaturday() ? DaysOfWeek.SAT : 0);
    }

    @Column(name = "SU", nullable = false)
    boolean sunday;
    @Column(name = "MO", nullable = false)
    boolean monday;
    @Column(name = "TU", nullable = false)
    boolean tuesday;
    @Column(name = "WE", nullable = false)
    boolean wednesday;
    @Column(name = "TH", nullable = false)
    boolean thursday;
    @Column(name = "FR", nullable = false)
    boolean friday;
    @Column(name = "SA", nullable = false)
    boolean saturday;

    // Spring data binding needs the accessor methods :-(
    public boolean isSunday() {
        return sunday;
    }

    public void setSunday(boolean sunday) {
        this.sunday = sunday;
    }

    public boolean isMonday() {
        return monday;
    }

    public void setMonday(boolean monday) {
        this.monday = monday;
    }

    public boolean isTuesday() {
        return tuesday;
    }

    public void setTuesday(boolean tuesday) {
        this.tuesday = tuesday;
    }

    public boolean isWednesday() {
        return wednesday;
    }

    public void setWednesday(boolean wednesday) {
        this.wednesday = wednesday;
    }

    public boolean isThursday() {
        return thursday;
    }

    public void setThursday(boolean thursday) {
        this.thursday = thursday;
    }

    public boolean isFriday() {
        return friday;
    }

    public void setFriday(boolean friday) {
        this.friday = friday;
    }

    public boolean isSaturday() {
        return saturday;
    }

    public void setSaturday(boolean saturday) {
        this.saturday = saturday;
    }

    /**
     * Get the day corresponding to the specified ISO day.
     */
    public boolean isCalendarDayISO(final int calDay) {
        switch (calDay) {
            case 1: return monday;
            case 2: return tuesday;
            case 3: return wednesday;
            case 4: return thursday;
            case 5: return friday;
            case 6: return saturday;
            case 7: return sunday;
            default:
                throw new IllegalArgumentException("Calendar day must be a valid DayOfWeek value");
        }
    }

    /**
     * Get the day corresponding to the specified java.util.Calendar.DAY_OF_WEEK property.
     */
    public boolean isCalendarDay(final int calDay) {
        switch (calDay) {
            case Calendar.SUNDAY: return sunday;
            case Calendar.MONDAY: return monday;
            case Calendar.TUESDAY: return tuesday;
            case Calendar.WEDNESDAY: return wednesday;
            case Calendar.THURSDAY: return thursday;
            case Calendar.FRIDAY: return friday;
            case Calendar.SATURDAY: return saturday;
            default:
                throw new IllegalArgumentException("Calendar day must be a valid java.util.Calendar.DAY_OF_WEEK property.");
        }
    }

    public boolean isJodaCalendarDay(final int calDay) {
        switch (calDay) {
            case DateTimeConstants.SUNDAY: return sunday;
            case DateTimeConstants.MONDAY: return monday;
            case DateTimeConstants.TUESDAY: return tuesday;
            case DateTimeConstants.WEDNESDAY: return wednesday;
            case DateTimeConstants.THURSDAY: return thursday;
            case DateTimeConstants.FRIDAY: return friday;
            case DateTimeConstants.SATURDAY: return saturday;
            default:
                throw new IllegalArgumentException("Calendar day must be a valid org.joda.time.DateTimeConstants property.");
        }
    }

    /**
     * Add to the 'checkDow' according to the number of days returned
     * to get the next day on or after 'checkDow'.
     */
    public Integer sameOrNextDayAfter(DayOfWeek checkDow) {
        if (checkDow == null) {
            return null;
        }
        // get ISO days of our sequence.
        List<Integer> days = toCalendarDayListISO();
        if (days.size() == 0) {
            return null;
        }

        // get ISO day to check, where Mon is 1.
        int checkDay = checkDow.getValue();

        // find the first day when checkDay >= day
        // eg checkDay Thurs >= days [Wed, Fri] -> Fri
        Optional<Integer> firstDayAfter = days.stream().filter(day -> day >= checkDay).findFirst();
        if (firstDayAfter.isPresent()) {
            // same day = 0, positive adds to checkDow - eg Fri - Thurs = 1, means adding 1 to Thurs = Fri
            return firstDayAfter.get() - checkDay;
        }

        // find the first day when checkDay < day
        // eg checkDay Thurs >= days [Mon, Tues] -> Tues
        Optional<Integer> firstDayBefore = days.stream().filter(day -> day < checkDay).findFirst();
        if (firstDayBefore.isPresent()) {
            // positive adds to checkDow - eg 7 - (Thurs - Mon) = 7 - (4-1) = 4, means adding 4 to Thurs = Mon
            return 7 - (checkDay - firstDayBefore.get());
        }

        return null;
    }

    /**
     * Set the day corresponding to ISO, where Mon is 1.
     */
    public void setCalendarDayFromISO(final int calDay, final boolean value) {
        switch (calDay) {
            case 1: monday = value; break;
            case 2: tuesday = value; break;
            case 3: wednesday = value; break;
            case 4: thursday = value; break;
            case 5: friday = value; break;
            case 6: saturday = value; break;
            case 7: sunday = value; break;
            default:
                throw new IllegalArgumentException("Calendar day must be a valid DayOfWeek value");
        }
    }

    /**
     * Set the day corresponding to ISO, where Mon is 1.
     */
    public void setCalendarDayFromISO(final DayOfWeek dow, final boolean value) {
        setCalendarDayFromISO(dow.getValue(), value);
    }

    /**
     * Set the day corresponding to the specified java.util.Calendar.DAY_OF_WEEK property.
     */
    public void setCalendarDay(final int calDay, final boolean value) {
        switch (calDay) {
            case Calendar.SUNDAY: sunday = value; break;
            case Calendar.MONDAY: monday = value; break;
            case Calendar.TUESDAY: tuesday = value; break;
            case Calendar.WEDNESDAY: wednesday = value; break;
            case Calendar.THURSDAY: thursday = value; break;
            case Calendar.FRIDAY: friday = value; break;
            case Calendar.SATURDAY: saturday = value; break;
            default:
                throw new IllegalArgumentException("Calendar day must be a valid java.util.Calendar.DAY_OF_WEEK property.");
        }
    }

    /**
     * Set the calendar days, where Sun is 1.
     */
    public void setCalendarDays(final List<Integer> calDays) {
        for (int calDay : calDays) {
            setCalendarDay(calDay, true);
        }
    }
    public void removeCalendarDays(final List<Integer> calDays) {
        for (int calDay : calDays) {
            setCalendarDay(calDay, false);
        }
    }

    public Set<Integer> toCalendarDaySet() {
        final ImmutableSet.Builder<Integer> builder = ImmutableSet.builder();
        if (sunday) { builder.add(Calendar.SUNDAY); }
        if (monday) { builder.add(Calendar.MONDAY); }
        if (tuesday) { builder.add(Calendar.TUESDAY); }
        if (wednesday) { builder.add(Calendar.WEDNESDAY); }
        if (thursday) { builder.add(Calendar.THURSDAY); }
        if (friday) { builder.add(Calendar.FRIDAY); }
        if (saturday) { builder.add(Calendar.SATURDAY); }
        return builder.build();
    }

    public List<Integer> toCalendarDayList() {
        return new ArrayList<>(toCalendarDaySet());
    }

    public List<Integer> toCalendarDayListISO() {
        final ImmutableList.Builder<Integer> builder = ImmutableList.builder();
        if (monday) { builder.add(DayOfWeek.MONDAY.getValue()); }
        if (tuesday) { builder.add(DayOfWeek.TUESDAY.getValue()); }
        if (wednesday) { builder.add(DayOfWeek.WEDNESDAY.getValue()); }
        if (thursday) { builder.add(DayOfWeek.THURSDAY.getValue()); }
        if (friday) { builder.add(DayOfWeek.FRIDAY.getValue()); }
        if (saturday) { builder.add(DayOfWeek.SATURDAY.getValue()); }
        if (sunday) { builder.add(DayOfWeek.SUNDAY.getValue()); }
        return builder.build();
    }

    public String toCalendarDaysCommaSepString() {
        final ImmutableList.Builder<String> builder = ImmutableList.builder();
        Joiner joiner = Joiner.on(",");
        if (sunday) { builder.add("Sun"); }
        if (monday) { builder.add("Mon"); }
        if (tuesday) { builder.add("Tue"); }
        if (wednesday) { builder.add("Wed"); }
        if (thursday) { builder.add("Thur"); }
        if (friday) { builder.add("Fri"); }
        if (saturday) { builder.add("Sat"); }
        return joiner.join( builder.build() );
    }

    public static DaysOfWeek allWeek() {
        return fromStringDays(new String[]{"mon", "tues", "wed", "thur", "fri", "sat", "sun"});
    }

    @Override
    public DaysOfWeek clone() {
        try {
            return (DaysOfWeek) super.clone();
        } catch (CloneNotSupportedException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof DaysOfWeek)) {
            return false;
        }

        DaysOfWeek that = (DaysOfWeek) o;

        return friday == that.friday && monday == that.monday && saturday == that.saturday && sunday == that.sunday && thursday == that.thursday && tuesday == that.tuesday && wednesday == that.wednesday;
    }

    @Override
    public int hashCode() {
        int result = (sunday ? 1 : 0);
        result = 31 * result + (monday ? 1 : 0);
        result = 31 * result + (tuesday ? 1 : 0);
        result = 31 * result + (wednesday ? 1 : 0);
        result = 31 * result + (thursday ? 1 : 0);
        result = 31 * result + (friday ? 1 : 0);
        result = 31 * result + (saturday ? 1 : 0);
        return result;
    }
}
