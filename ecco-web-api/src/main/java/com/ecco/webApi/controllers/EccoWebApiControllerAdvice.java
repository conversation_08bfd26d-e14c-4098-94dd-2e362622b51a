package com.ecco.webApi.controllers;

import com.ecco.security.SecurityUtil;
import com.ecco.webApi.viewModels.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.InvalidDataAccessApiUsageException;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.transaction.TransactionSystemException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolationException;
import java.util.ConcurrentModificationException;
import java.util.List;
import java.util.stream.Collectors;

import static java.lang.System.getProperty;

@RestControllerAdvice
@Slf4j
public class EccoWebApiControllerAdvice {

    private boolean isDevMode() {
        // TODO: Establish why we couldn't autowire EccoEnvironment
        return "dev".equals(getProperty("env"));
    }

    // TODO: Establish why this does not send a JSON response when
    // http://stackoverflow.com/questions/3230358/spring-3-create-exceptionhandler-for-nosuchrequesthandlingmethodexception
    // says it should work
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    @ResponseBody
    public Result handleException(Exception e, HttpServletResponse response) {
        BaseWebApiController.dontCache(response);

        // Silently handle client abort when user navigates during an Ajax request
        if (e.getClass().getSimpleName().equals("ClientAbortException")) {
            return null; // returning anything else would try to write to the "broken pipe"
        }

        // for 500 errors it is helpful to know the user who requested it
        String username = SecurityUtil.authenticatedUserExists()
                ? SecurityUtil.getAuthenticatedUsername() : "";

        log.error(e.getMessage() + " [username: " + username + "]", e);

        // Explicitly don't handle security exceptions this way as Spring Security should translate
        if (e instanceof AccessDeniedException
                || e instanceof AuthenticationException) {
            throw (RuntimeException)e;
        }

        if (e.getCause() instanceof org.hibernate.exception.ConstraintViolationException) {
            return new Result(e.getMessage() + " [" + e.getCause().getCause().getMessage() + "]");
        }
        return new Result(e);
    }

    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result handleConstraintViolation(ConstraintViolationException e, HttpServletResponse response) {
        BaseWebApiController.dontCache(response);
        String message = e.getConstraintViolations().stream()
                .map(v -> String.format("validation failed on %s for '%s' %s", v.getRootBeanClass(), v.getPropertyPath(), v.getMessage()))
                .collect(Collectors.joining(", "));
        return new Result(message);
    }

    @ExceptionHandler(TransactionSystemException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ResponseBody
    public Result handleTransactionExceptions(TransactionSystemException e, HttpServletResponse response) {
        BaseWebApiController.dontCache(response);
        // Handle constraint violations
        if (e.getMostSpecificCause() instanceof ConstraintViolationException) {
            log.error(e.getMessage(), e);
            // If we are doing full form submissions we could go further, but for now, this is
            // just a fallback to avoid the user getting junk.
            return new Result("Input validation failed. Please enter valid data");
        }
        log.error(e.getMostSpecificCause().getMessage(), e);
        return new Result(e.getMostSpecificCause().getMessage());
    }

    @ExceptionHandler(EntityAlreadyExistsException.class)
    @ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
    @ResponseBody
    public Result handleAlreadyExistsException(EntityAlreadyExistsException e, HttpServletResponse response) {
        BaseWebApiController.dontCache(response);
        return new Result(e, e.getId().toString());
    }

    @ExceptionHandler(NotFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public Result handleNotFoundException(NotFoundException e, HttpServletResponse response) {
        BaseWebApiController.dontCache(response);
        return new Result(e, e.getId().toString());
    }

    @ExceptionHandler(NoContentException.class)
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public Object handleNoContentException() {
        // This result can be cached, so we don't prevent caching here
        return null;
    }


    @ExceptionHandler
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ResponseBody
    public Result handleIllegalArgumentException(IllegalArgumentException e, HttpServletResponse response) {
        BaseWebApiController.dontCache(response);
        log.warn("400 - Bad Request due to: {}", e.getMessage());
        if (isDevMode()) log.info("Bad Request stack: ", e);
        return new Result(e);
    }

    @ExceptionHandler
    @ResponseBody
    public Result handleDataApiException(InvalidDataAccessApiUsageException e, HttpServletResponse response) {
        BaseWebApiController.dontCache(response);
        log.error(e.getMessage(), e);
        if (e.getMostSpecificCause() instanceof IllegalArgumentException) {
            response.setStatus(HttpStatus.BAD_REQUEST.value());
            return new Result(e.getMostSpecificCause().getMessage());
        }
        response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
        log.warn("Server error due to: {}", e.getMessage());
        return new Result(e);
    }

    @ExceptionHandler(ConcurrentModificationException.class)
    @ResponseStatus(HttpStatus.CONFLICT)
    @ResponseBody
    public Result handleConcurrentModificationException(ConcurrentModificationException e, HttpServletResponse response) {
        BaseWebApiController.dontCache(response);
        log.warn("Conflict due to: {}", e.getMessage());
        if (isDevMode()) log.info("Full exception: ", e);
        return new Result(e);
    }
}
