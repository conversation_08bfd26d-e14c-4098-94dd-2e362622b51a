<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd"
                   logicalFilePath="classpath:sql/1.1-changes/015-editClientModules.xml">

    <!-- client perspective is at /clients/ -->
    <changeSet author="default" id="ECCO-133-ClientPerspective-url">
        <update tableName="menuitem">
            <column name="url" value="/dynamic/secure/entities/clients/get.html"/>
            <column name="linkText" value="menu.linktext.clients"/>
            <where>id=27</where>
        </update>
    </changeSet>

</databaseChangeLog>
