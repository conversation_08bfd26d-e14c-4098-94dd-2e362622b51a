package com.ecco.resources.cachebust;

import com.ecco.offline.CachedResource;
import com.ecco.offline.FallbackResource;
import com.ecco.offline.OfflineResourceNotFoundException;
import com.ecco.offline.OfflineResourceProvider;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.HandlerMapping;

import javax.annotation.Nonnull;
import javax.servlet.http.HttpServletRequest;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

/**
 * TODO: This mostly duplicates OfflineResourceController, but exposes just those providers
 * Perhaps we could share common bits.
 */
@Controller
@RequiredArgsConstructor
public class OnlineResourceController implements ApplicationListener<ContextRefreshedEvent> {

    private final ApplicationContext applicationContext;
    private final Collection<OfflineResourceProvider> offlineResourceProviders;
    private final Map<String, ResponseEntity<byte[]>> responseEntitiesByPath = new HashMap<>();

    @Value("${ecco.mvc.resourcesPath:}")
    private String resourcePath;

    @Override
    public void onApplicationEvent(@Nonnull ContextRefreshedEvent event) {
        if (!event.getApplicationContext().equals(applicationContext)) {
            return;
        }

        for (OfflineResourceProvider offlineResourceProvider : offlineResourceProviders) {
            offlineResourceProvider.init();

            for (CachedResource cachedResource : offlineResourceProvider.getCachedResources()) {
                responseEntitiesByPath.put(cachedResource.getPath(), cachedResource.getResponseEntity());
            }

            for (FallbackResource fallbackResource : offlineResourceProvider.getFallbackResources()) {
                responseEntitiesByPath.put(fallbackResource.getPath(), fallbackResource.getResponseEntity());
            }
        }
    }

    // NOTE: Just exposing specific ones that are generated. The rest are done elsewhere
    @RequestMapping(value = {
            "${ecco.mvc.resourcesPath:}/{cacheBust}/scripts/application-properties.js",
            "${ecco.mvc.resourcesPath:}/{cacheBust}/scripts/messages.js",
    },
            method = RequestMethod.GET)
    public ResponseEntity<byte[]> getResource(
            @PathVariable String cacheBust,
            WebRequest request, HttpServletRequest servletRequest)
            throws OfflineResourceNotFoundException {
        // We can't use a @PathAttribute to match the resource path, because it would only match a single path segment.
        // So we extract the path from the HttpServletRequest instead.
        String path = ((String) servletRequest.getAttribute(HandlerMapping.PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE));
        path = path.substring(resourcePath.length() + 2 + cacheBust.length()); // Strip leading "/{cacheBust}/"

        ResponseEntity<byte[]> responseEntity = responseEntitiesByPath.get(path);

        if (responseEntity == null) {
            throw new OfflineResourceNotFoundException(path);
        }

        HttpHeaders responseHeaders = responseEntity.getHeaders();
        if (request.checkNotModified(responseHeaders.getLastModified())
                || request.checkNotModified(responseHeaders.getETag())) {
            return null; // request.checkNotModified transparently sets HTTP 304 Not Modified
        } else {
            return responseEntity;
        }
    }
}
