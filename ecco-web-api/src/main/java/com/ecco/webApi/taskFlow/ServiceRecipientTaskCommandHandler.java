package com.ecco.webApi.taskFlow;

import com.ecco.webApi.CommandResult;
import com.ecco.webApi.controllers.WorkflowTaskController;
import org.springframework.security.core.Authentication;
import org.springframework.util.Assert;

import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.commands.ServiceRecipientTaskCommand;
import com.ecco.dom.commands.ReferralTaskUpdateCommand;
import com.ecco.webApi.evidence.ServiceRecipientCommandHandler;
import com.fasterxml.jackson.databind.ObjectMapper;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.io.Serializable;

public abstract class ServiceRecipientTaskCommandHandler<VM extends ServiceRecipientTaskCommandViewModel> extends
        ServiceRecipientCommandHandler<VM, ServiceRecipientTaskCommand, ServiceRecipientTaskParams> {

    protected final WorkflowTaskController workflowTaskController;


    public ServiceRecipientTaskCommandHandler(ObjectMapper objectMapper,
                                              WorkflowTaskController workflowTaskController,
                                              ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                                              Class<VM> vmClass) {
        super(objectMapper, serviceRecipientCommandRepository, vmClass);
        this.workflowTaskController = workflowTaskController;
    }

    @Override
    protected ReferralTaskUpdateCommand createCommand(Serializable targetId, ServiceRecipientTaskParams params, String requestBody,
                                                      ServiceRecipientTaskCommandViewModel viewModel, long userId) {
        if (params.serviceRecipientId != null) {
            Assert.state(params.serviceRecipientId.equals(viewModel.serviceRecipientId), "serviceRecipientId in body must match URI");
        }
        Assert.state(params.taskName.equals(viewModel.taskName), "taskName in body must match URI");

        return new ReferralTaskUpdateCommand(viewModel.uuid, viewModel.timestamp, userId,
                requestBody, params.serviceRecipientId != null ? params.serviceRecipientId : (Integer) targetId, params.taskName);
    }

    protected abstract CommandResult handleTaskInternal(Authentication auth, ServiceRecipientTaskParams serviceRecipientTaskParams, VM viewModel);

    @Nullable
    @Override
    protected final CommandResult handleInternal(@Nonnull Authentication auth, ServiceRecipientTaskParams serviceRecipientTaskParams, @Nonnull VM viewModel) {
        var result = handleTaskInternal(auth, serviceRecipientTaskParams, viewModel);
        // A comment in ReferralTaskStatusCommandViewModel points out that taskInstanceId can replace taskHandle for activiti/linear
        // to represent the actual task.
        if (canCompleteWorkflowTaskIfPossible()) {
            workflowTaskController.completeWorkflowTaskIfPossible(viewModel.taskHandle);
        }
        return result;
    }

    protected boolean canCompleteWorkflowTaskIfPossible() {
        return true;
    }
}
