package com.ecco.service.security;

import com.ecco.dom.ClientDetail;
import com.ecco.dom.Individual;
import com.ecco.security.dom.Group;
import com.ecco.security.dom.GroupMember;
import com.ecco.security.dom.User;
import com.ecco.security.service.UserManagementService;
import com.ecco.testsupport.AbstractIntegrationTest;
import com.google.common.collect.Lists;
import org.joda.time.DateTime;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.test.annotation.Rollback;

import java.util.HashSet;
import java.util.Set;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.greaterThan;
import static org.junit.Assert.*;

@Rollback
public class UserManagementService_IT extends AbstractIntegrationTest {

    @Autowired
    private UserManagementService userManagementService;

    private static final String STAFF_GROUP_NAME = "test-grp-staff";
    private static final String SYSADMIN_GROUP_NAME = "test-grp-sysadmin";

    public static final String SYSADMIN_USERNAME = "sysadmin";

    private static final String NORMAL_USERNAME = "nobby";
    private static final String ACTIVE_USER = "goodie";

    @Before
    public void setup() {
        GrantedAuthority STAFF = new SimpleGrantedAuthority("ROLE_STAFF");
        userManagementService.createGroup(STAFF_GROUP_NAME, Lists.newArrayList(STAFF));

        GrantedAuthority SYSADMIN = new SimpleGrantedAuthority("ROLE_SYSADMIN");
        userManagementService.createGroup(SYSADMIN_GROUP_NAME, Lists.newArrayList(SYSADMIN));

        flushAndClear();

        User u = createUser(NORMAL_USERNAME, "Nobby", "Stiles");
        addUserToGroup(STAFF_GROUP_NAME, u);
        userManagementService.createUser(u);

        User sysadmin = createUser(SYSADMIN_USERNAME, "Sissy", null);
        addUserToGroup(STAFF_GROUP_NAME, sysadmin);
        userManagementService.createUser(sysadmin);

        UsernamePasswordAuthenticationToken token =
                new UsernamePasswordAuthenticationToken(ACTIVE_USER, "twoshoes", AuthorityUtils.createAuthorityList("ROLE_SYSADMIN"));
        SecurityContext securityContext = SecurityContextHolder.getContext();
        securityContext.setAuthentication(token);
    }

    private User createUser(String userName, String firstName, String lastName) {
        Individual individual = Individual.builder(firstName, lastName).withNewUser(userName, "s3cr3t").build();
        //noinspection deprecation
        return individual.getUser();
    }
    private void addUserToGroup(String groupName, User u) {
        Group g = userManagementService.findGroupByName(groupName);
        if (u.getGroupMemberships() == null) {
            u.setGroupMemberships(new HashSet<>());
        }
        u.getGroupMemberships().add(new GroupMember(g, u));
    }
    @SuppressWarnings("unused")
    private void removeUserFromGroup(String groupName, User u) {
        Group g = userManagementService.findGroupByName(groupName);
        GroupMember toRemove = findGroupMembership(g, u.getGroupMemberships());
        u.getGroupMemberships().remove(toRemove);
    }
    private GroupMember findGroupMembership(Group group, Set<GroupMember> groupMemberships) {
        for (GroupMember m : groupMemberships) {
            if (m.getGroup().equals(group)) {
                return m;
            }
        }
        return null;
    }

    @Test
    public void shouldCreateNoConflictingUsernames() {
        // Should allow to create multiple clients with same name
        for (int i = 0; i < 4; i++) {
            ClientDetail cd1 = new ClientDetail();
            cd1.setContact(Individual.builder("freddie", "mercury (O, great one)").build());
            userManagementService.generateClientUser(cd1.getDisplayName(), cd1.getContact());
            flushAndClear();
        }
    }

    @Test
    public void shouldCreateUserWithNo_lastLoggedIn() {
        User u = createUser("raysmith", "Ray", "Smith");
        addUserToGroup(STAFF_GROUP_NAME, u);
        userManagementService.createUser(u);

        flushAndClear();
        assertNull(u.getLastLoggedIn());
    }

    @Test
    public void shouldLoadUserByUsername() {
        UserDetails user = userManagementService.loadUserByUsername(SYSADMIN_USERNAME);
        assertEquals(SYSADMIN_USERNAME, user.getUsername());

    }

    @Test(expected = UsernameNotFoundException.class)
    public void shouldThrowExceptionForUnknownUsername() {
        userManagementService.loadUserByUsername("nobody");
    }

    @Test
    public void shouldDetermineUserExists() {
        assertTrue(userManagementService.userExists(SYSADMIN_USERNAME));
    }

    @Test
    public void shouldCreateUser() {
        UserDetails user = userManagementService.loadUserByUsername(NORMAL_USERNAME);
        assertEquals(NORMAL_USERNAME, user.getUsername());
    }

    @Test
    public void shouldCreateUserWithContact() {
        User user = userManagementService.loadUserByUsername(NORMAL_USERNAME);
        assertEquals(NORMAL_USERNAME, user.getUsername());
        assertEquals("Nobby", user.getContact().getFirstName());
    }

    @Test
    public void shouldCreateUserWithGroupMembership() {

        User user = userManagementService.loadUserByUsername(NORMAL_USERNAME);
        assertTrue(user.isEnabled());
        assertThat(user.getGroupMemberships().iterator().next().getGroup().getName(), is(STAFF_GROUP_NAME));
    }

    @Test
    public void shouldUpdateUser() {

        User user = userManagementService.loadUserByUsername(NORMAL_USERNAME);
        assertTrue(user.isEnabled());

        user.setEnabled(false);
        userManagementService.updateUser(user);
        user = userManagementService.loadUserByUsername(NORMAL_USERNAME);
        assertFalse(user.isEnabled());
    }

    @Test
    public void shouldUpdateUserGroupMemberships() {

        User user = userManagementService.loadUserByUsername(NORMAL_USERNAME);
        assertEquals(STAFF_GROUP_NAME, user.getGroups().iterator().next());

        flushAndClear();

        Set<String> targetGroups = new HashSet<>();
        // target is to have sysadmin group, NOT staff group as initially setup
        targetGroups.add(SYSADMIN_GROUP_NAME);
        // as per the ui - we change the groups
        user.setNewGroups(targetGroups);
        userManagementService.updateUser(user);

        flushAndClear();

        user = userManagementService.loadUserByUsername(NORMAL_USERNAME);
        assertEquals(1, user.getGroupMemberships().size());
        assertEquals(SYSADMIN_GROUP_NAME, user.getGroupMemberships().iterator().next().getGroup().getName());
    }

    @Test
    public void shouldUpdateUserGroupMembershipsWithNothing() {

        User user = userManagementService.loadUserByUsername(NORMAL_USERNAME);
        assertEquals(STAFF_GROUP_NAME, user.getGroups().iterator().next());

        flushAndClear();

        Set<String> targetGroups = null;
        // as per the ui - we change the groups
        user.setNewGroups(targetGroups);
        userManagementService.updateUser(user);

        flushAndClear();

        user = userManagementService.loadUserByUsername(NORMAL_USERNAME);
        assertEquals(0, user.getGroupMemberships().size());
    }

    @Test
    public void shouldUpdateUserGroupMemberships_internal() {

        User user = userManagementService.loadUserByUsername(NORMAL_USERNAME);
        assertEquals(STAFF_GROUP_NAME, user.getGroups().iterator().next());

        Set<String> targetGroups = new HashSet<>();
        // target is to have sysadmin group, NOT staff group as initially setup
        targetGroups.add(SYSADMIN_GROUP_NAME);
        //noinspection UnusedAssignment
        user = userManagementService.mergeNewGroups(user, targetGroups);

        flushAndClear();

        user = userManagementService.loadUserByUsername(NORMAL_USERNAME);
        assertEquals(1, user.getGroupMemberships().size());
        assertEquals(SYSADMIN_GROUP_NAME, user.getGroupMemberships().iterator().next().getGroup().getName());
//        shouldFindUsersInTestSysadminGroup(); TODO: This may be fully broken!
        shouldFindAllGroups();
    }

    @Test(expected = UsernameNotFoundException.class)
    public void shouldDeleteUser() {

        UserDetails user = userManagementService.loadUserByUsername(NORMAL_USERNAME);
        assertEquals(NORMAL_USERNAME, user.getUsername());

        userManagementService.deleteUser(NORMAL_USERNAME);
        userManagementService.loadUserByUsername(NORMAL_USERNAME); // should throw
    }

    private void shouldFindAllGroups() {
        assertThat(userManagementService.findAllGroups().size(), greaterThan(0));
    }

    private void shouldFindUsersInTestSysadminGroup() {
        assertThat(userManagementService.findUsersInGroup(SYSADMIN_GROUP_NAME).size(), greaterThan(0));
    }

    @Test
    public void shouldRecordLoginTime() {

        final DateTime loginTime = new DateTime();
        userManagementService.recordSuccessfulLoginTime(NORMAL_USERNAME, loginTime);

        User user = userManagementService.loadUserByUsername(NORMAL_USERNAME);
        assertEquals(loginTime, user.getLastLoggedIn());
    }

}
