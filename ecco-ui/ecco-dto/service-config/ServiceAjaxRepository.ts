import {Services} from "../service-config-domain";
import {ProjectDto, ServiceDto} from "../service-config-dto";
import {ServiceRepository} from "./ServiceRepository";
import {ApiClient} from "../web-api";

/* This interface must match the Java class com.ecco.webApi.viewModels.ServicesViewModel. */
interface ServicesDto {
    services: ServiceDto[];
}

export class ServiceAjaxRepository implements ServiceRepository {

    private cacheAllServices: Promise<Services> | null = null;

    constructor(private apiClient: ApiClient) {
    }

    public findAllServices(): Promise<Services> {
        var allServices = this.cacheAllServices;
        if (!allServices) {
            const path = "service/";

            allServices = this.apiClient.get<ServiceDto[]>(path)
                .then( services => new Services(services) );
            this.cacheAllServices = allServices;
        }
        return allServices;
    }

    public findOneServiceDto(serviceId: number): Promise<ServiceDto> {
        const path = `service/${serviceId}/`;
        return this.apiClient.get<ServiceDto>(path);
    }

    /*public findOneService(serviceId: number): Promise<Service> {
        const path = `service/${serviceId}/`;
        return this.apiClient.get<ServiceDto>(path)
            .then( (dto) => new Service(dto) );
    }*/

    public findAllServicesProjects(): Promise<ServiceDto[]> {
        const path = "servicesProjects/";
        return this.apiClient.get<ServicesDto>(path).then(
            servicesWrapper => servicesWrapper.services
        );
    }

    public findAllProjects(): Promise<ProjectDto[]> {
        const path = "project/";
        return this.apiClient.get<ProjectDto[]>(path);
    }
}
