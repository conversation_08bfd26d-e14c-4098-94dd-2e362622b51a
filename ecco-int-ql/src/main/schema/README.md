NB The original playground for the integration is https://github.com/eccosolutions/poc-ecco-int-ql/
The .wsdl and .xsd here are those from the QL WSDL service,
but then trimmed down significantly using subset.xsl before being
committed.

To update:
  - cd this dir
  - download xalan and serializer.jar to it
  - ```java -jar xalan.jar -in QLWCFServiceFull.wsdl.example -xsl subset.xsl -out QLWCFService.wsdl```
  - Use IDEA to format the results
  - Run mvn install to generate the java code
