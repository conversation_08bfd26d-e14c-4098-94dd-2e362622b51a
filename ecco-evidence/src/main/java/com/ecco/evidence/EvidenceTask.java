package com.ecco.evidence;

import com.ecco.dom.EvidenceGroup;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;


/**
 * The evidence sourceTask to indicate the task this evidence was made on.
 * TODO THIS CLASS IS NOT OFTEN USED AND COULD BE REMOVED -
 * TODO Actually it would be better to have as instances of content of taskdefinitions table but at webApi - e.g. WorkflowTaskDefinition has taskDefinitionKey which is ra.name
 * Currently this class is used to:
 *  - get the questionnaire evidencegroup for the handler (though this should come from the url)
 *  - understand how to save 'outcomes' from the admin page, ie support/threat (but this could come from the url)
 *  - load the audit info next to tasks and filter to this list - (but we could remove the filter)
 * @see EvidenceGroup
 */
@EqualsAndHashCode
public class EvidenceTask {

    // static definitions for testing,from the taskdefinitions table
    public static EvidenceTask GROUP_SUPPORT = new EvidenceTask("groupSupport"); // also known as groupActivities
    public static EvidenceTask QUESTIONNAIRE_IAPTFEEDBACK = new EvidenceTask("iaptFeedback");
    public static EvidenceTask QUESTIONNAIRE_GENERAL = new EvidenceTask("generalQuestionnaire");
    public static EvidenceTask CUSTOMFORM_1 = new EvidenceTask("customForm1");
    public static EvidenceTask STAFF_NOTES = new EvidenceTask("supportStaffNotes");
    public static EvidenceTask NEEDS_ASSESSMENT = new EvidenceTask("needsAssessment");
    public static EvidenceTask NEEDS_REDUCTION = new EvidenceTask("needsReduction");
    public static EvidenceTask CARE_PLAN = new EvidenceTask("carePlan");
    public static EvidenceTask ROTA_VISIT = new EvidenceTask("rotaVisit");
    public static EvidenceTask THREAT_ASSESSMENT = new EvidenceTask("threatAssessment");
    public static EvidenceTask THREAT_ASSESSMENT_REDUCTION = new EvidenceTask("threatAssessmentReduction");

    private final String name;

    public EvidenceTask(String name) {
        this.name = name;
    }

    public static EvidenceTask fromTaskName(String taskName) {
        return new EvidenceTask(taskName);
    }

    public String getTaskName() {
        return name;
    }

}
