package com.ecco.dom.commands;

import java.util.UUID;

import org.hibernate.annotations.Type;
import org.joda.time.Instant;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.persistence.*;

import com.ecco.infrastructure.dom.BaseLongKeyedCommand;

/**
 * Commands around report definitions with typical subclasses for updating, deleting etc.
 */
@Entity
@Table(name = "repdef_commands")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "commandname", discriminatorType = DiscriminatorType.STRING)
public abstract class ReportDefinitionCommand extends BaseLongKeyedCommand {

    @Nonnull
    @Column(name = "reportDefUuid", nullable = false, columnDefinition = "CHAR(36)")
    @Type(type = "uuid-char")
    private UUID reportDefUuid;

    protected ReportDefinitionCommand(@Nullable UUID uuid, @Nonnull Instant remoteCreationTime,
            long userId, @Nonnull String body, @Nonnull UUID reportDefUuid) {
        super(uuid, remoteCreationTime, userId, body);
        this.reportDefUuid = reportDefUuid;
    }

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    protected ReportDefinitionCommand() {
        super();
    }

    public UUID getReportDefUuid() {
        return reportDefUuid;
    }

}
