import $ = require("jquery");
import BaseControl = require("./BaseControl");
import InputControl = require("./InputControl");
import CheckboxInput = require("./CheckboxInput");
import {ValidationCheck, ValidationChecksBuilder, ValidationErrors} from "../common/validation";
import InputValidation = require("../controls/InputValidation");

class CheckboxGroupInput extends BaseControl implements InputControl {

    private onChange: (value: string, state: boolean) => void;
    // private $input: $.JQuery;
    private checkboxInputs: CheckboxInput[] = [];
    private valid = true;
    private validation: InputValidation;
    private id: string;

    public constructor(id?: string) {
        // correct a bootstrap css which for us creates a small box around radio buttons, rather than a background-box
        super($("<fieldset>").css('height', 'auto').css('margin-top', 0));
        this.id = id ? id : "some id";
    }

    public populateFromList<L>(items:L[], extractor: (item: L) => {label: string; id: string; selected: boolean; value: string; readOnly: boolean, icon?: $.JQuery}) {

        for (let i = 0; i < this.checkboxInputs.length; i++) {
            this.element().empty();
        }
        this.checkboxInputs = [];

        $.each(items, (i, item: L) => {
            const ci = new CheckboxInput(extractor(item).label, extractor(item).id, extractor(item).value, extractor(item).icon);
            ci.setChecked(extractor(item).selected);
            if (extractor(item).readOnly) {
                ci.setReadOnly()
            }
            ci.change(this.onChange);
            this.checkboxInputs.push(ci);
            this.element().append(ci.element());
        });

        return this;
    }

    public change( onChange: (value: string, state: boolean) => void ): this {
        if (this.onChange) {
            throw new Error("Error: Don't attach multiple event handlers");
        }
        this.onChange = (value: string, state: boolean) => {
            this.validation && this.validation.validate(value);
            onChange(value, state);
        };
        return this;
    }

    public checked(): string[] {
        const values: string[] = [];
        this.checkboxInputs.forEach((input: CheckboxInput) => {
            if (input.isChecked()) {
                values.push(input.getValue());
            }
        });
        return values;
    }

    public withValidationChecks(checks: ValidationChecksBuilder,
                                errors: ValidationErrors,
                                validationCallBack: (valid: boolean, message?: string) => void) {
        if (this.onChange) {
            throw new Error("Error: Call withValidationChecks before change");
        }
        this.validation = new InputValidation(this.id, this, this, checks, errors, validationCallBack);
        return this;
    }
    public enableValidation() {
        this.validation.primeValidation();
    }

    public validate(field: string, checks: ValidationChecksBuilder, errors: ValidationErrors) {
        if (checks.isChecked(ValidationCheck.Required)) {
            // bit crazy to focce a tickbox but could be regulatory
            errors.requireNotEmpty(field, this.checked());
        }
        this.valid = errors.isValid();
    }

    public getValuesOrNull(hasInitialValue: boolean): CheckboxInput[] {
        const hasValues: CheckboxInput[] = [];
        this.checkboxInputs.forEach((input: CheckboxInput) => {
            if (input.getValueOrNull(hasInitialValue) !== null) {
                hasValues.push(input);
            }
        });
        return hasValues.length > 0
            ? hasValues
            : null;
    }

}

export = CheckboxGroupInput;
