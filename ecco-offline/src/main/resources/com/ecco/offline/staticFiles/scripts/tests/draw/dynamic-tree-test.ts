import QUnit = require("qunit");
import dynamicTree = require("../../draw/dynamic-tree");
import Node = dynamicTree.DynamicTreeNode;
import $ = require("jquery");

var tree = new dynamicTree.DynamicTreeControl(960, 960);
tree.attach($("#canvas"));
var root = new Node("root");
tree.setContextNode(root);
var child1 = new Node("child 1");
root.addChild(child1);
child1.addAnnotation(new dynamicTree.AnnotationNode("15%"));
child1.addChild(new Node("child 1a"));
root.addChild(new Node("child 2"));
var child3 = new Node("child 3");
root.addChild(child3);
var child3a = new Node("child 3a");
child3a.addAnnotation(new dynamicTree.AnnotationNode("75%"));
child3a.addAnnotation(new dynamicTree.AnnotationNode("A"));
child3.addChild(child3a);
child3.addChild(new Node("child 3b"));
child3.addChild(new Node("child 3c"));

var contextNode = root;

$("<button>")
    .text("swap subtree")
    .click(() => {
        if (contextNode == root) {
            contextNode = child3;
        } else {
            contextNode = root;
        }

        tree.setContextNode(contextNode);
    })
    .appendTo(document.body);

QUnit.test('SVG should be present', () => {
    // Dubious because Raphaël uses alternatives to SVG where SVG is unavailable.
    QUnit.equal(tree.element().find("svg").length, 1, "There should be an SVG element in #canvas");
});


QUnit.load();
QUnit.start();
