package com.ecco.webApi.taskFlow;


import com.ecco.dto.ChangeViewModel;

import javax.annotation.Nullable;

public class ReferralTaskEditDestinationCommandViewModel extends ServiceRecipientTaskCommandViewModel {

    static String TASK_EDIT_DESTINATION = "editDestination";

    @Nullable
    public ChangeViewModel<Integer> projectChange;

    ReferralTaskEditDestinationCommandViewModel() {
        super();
    }

    public ReferralTaskEditDestinationCommandViewModel(int serviceRecipientId, String taskHandle) {
        super(serviceRecipientId, TASK_EDIT_DESTINATION, taskHandle);
    }

}
