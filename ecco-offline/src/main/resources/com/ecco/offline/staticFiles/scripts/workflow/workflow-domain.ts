import * as dto from "ecco-dto/workflow-dto";

export class Workflow {
    private tasks: Task[] = [];

    constructor(workflowDto: dto.WorkflowDto, taskDefinitionDtosByHandle: { [key: string]: dto.TaskDefinitionDto; }) {
        if (!workflowDto) {
            throw new Error("workflowDto must be specified");
        }

        if (!taskDefinitionDtosByHandle) {
            throw new Error("taskDefinitionDtosByHandle must be specified");
        }

        for (var i = 0; i < workflowDto.tasks.length; ++i) {
            var taskDto = workflowDto.tasks[i];
            var taskDefinitionDto = taskDefinitionDtosByHandle[taskDto.taskDefinitionHandle];

            if (!taskDefinitionDto) {
                throw new Error("Missing TaskDefinitionDto for handle '" + taskDto.taskDefinitionHandle + "'");
            }

            this.tasks.push(new Task(taskDto, taskDefinitionDto));
        }
    }

    /** Iterates over the Tasks, and calls callback(task) for each Task. */
    public forEachTask(callback: (task: Task) => void) {
        for (var i = 0; i < this.tasks.length; ++i) {
            callback(this.tasks[i]);
        }
    }

    public getTasks() {
        return this.tasks;
    }
}

export class Task {
    private available: boolean;
    private completed: boolean;
    private taskDefinitionHandle: string;
    private taskName: string;

    private taskType: string;
    private formSettings: FormSettings;

    constructor(taskDto: dto.TaskDto, taskDefinitionDto: dto.TaskDefinitionDto) {
        if (!taskDto) {
            throw new Error("taskDto must be specified");
        }

        if (!taskDefinitionDto) {
            throw new Error("taskDefinitionDto must be specified");
        }

        if (taskDto.taskDefinitionHandle !== taskDefinitionDto.handle) {
            throw new Error("taskDto.taskDefinitionHandle !== taskDefinitionDto.handle");
        }

        this.available = taskDto.isAvailable;
        this.completed = taskDto.isCompleted;
        this.taskName = taskDto.taskName;

        this.taskType = taskDefinitionDto.taskType;
        this.formSettings = taskDefinitionDto.formSettings;
    }

    public getTaskName() {
        return this.taskName;
    }

    public getTaskDefinitionHandle() {
        return this.taskDefinitionHandle;
    }

    public getTaskType() {
        return this.taskType;
    }

    public getAvailable() {
        return this.available;
    }

    public getCompleted() {
        return this.completed;
    }
}

export interface FormSettings {
    [key: string]: string;
}