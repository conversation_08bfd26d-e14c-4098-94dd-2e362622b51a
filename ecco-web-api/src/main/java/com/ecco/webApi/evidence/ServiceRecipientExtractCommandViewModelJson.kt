package com.ecco.webApi.evidence

import com.ecco.dom.ReferralServiceRecipient
import com.ecco.dom.servicerecipients.BaseServiceRecipientCommand
import com.ecco.infrastructure.hibernate.AntiProxyUtils
import com.ecco.security.repositories.UserRepository

/** Extracts the raw JSON body and enhances it by prefixing the latest properties to the DTO for simpler rendering.  */
class ServiceRecipientExtractCommandViewModelJson<T : BaseServiceRecipientCommand>(userRepository: UserRepository) :
    GoalCommandExtractCommandViewModelJson<T>(userRepository) {
    override fun applyAdditionalProperties(inputSR: T): String {
        val displayName = (",\"displayName\":\""
                + inputSR.serviceRecipient.displayName
                + "\"")
        if (ReferralServiceRecipient.PREFIX == inputSR.serviceRecipient.prefix) {
            val target = AntiProxyUtils.deproxy(inputSR.serviceRecipient)
            val input = target as ReferralServiceRecipient
            val clientCode = (",\"latestClientCode\":\""
                    + input.referral.client.code
                    + "\"")
            val referralCode = (",\"latestReferralCode\":\""
                    + input.referral.idCode
                    + "\"")
            val referralId = (",\"latestReferralId\":\""
                    + input.referral.id
                    + "\"")
            val clientId = (",\"latestClientId\":\""
                    + input.referral.client.id
                    + "\"")
            val serviceAllocId = (",\"latestServiceAllocationId\":\""
                    + input.serviceAllocationId
                    + "\"")
            return displayName + clientCode + referralId + serviceAllocId
        }
        return ""
    }
}