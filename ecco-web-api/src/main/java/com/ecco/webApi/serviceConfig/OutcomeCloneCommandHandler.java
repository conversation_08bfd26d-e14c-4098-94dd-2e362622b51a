package com.ecco.webApi.serviceConfig;

import com.ecco.infrastructure.dom.ConfigCommand;
import com.ecco.serviceConfig.dom.*;
import com.ecco.serviceConfig.repositories.ConfigCommandRepository;
import com.ecco.serviceConfig.repositories.OutcomeRepository;
import com.ecco.serviceConfig.viewModel.OutcomeFromViewModel;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.evidence.BaseCommandHandler;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import javax.annotation.Nonnull;
import java.io.Serializable;

@Component
public class OutcomeCloneCommandHandler extends BaseCommandHandler<OutcomeCloneCommandViewModel, Long,
        ConfigCommand, Object> {

    private final OutcomeFromViewModel outcomeFromViewModel = new OutcomeFromViewModel();

    @Nonnull
    private final OutcomeRepository outcomeRepository;

    @Autowired
    public OutcomeCloneCommandHandler(ObjectMapper objectMapper, ConfigCommandRepository configCommandRepository,
                                 OutcomeRepository outcomeRepository) {
        super(objectMapper, configCommandRepository, OutcomeCloneCommandViewModel.class);
        this.outcomeRepository = outcomeRepository;
    }

    @Override
    protected CommandResult handleInternal(@NotNull Authentication auth, Object params, @NotNull OutcomeCloneCommandViewModel viewModel) {
        this.addOutcome(auth, viewModel);
        return null;
    }

    private void addOutcome(Authentication auth, OutcomeCloneCommandViewModel cmdVM) {
        Outcome existing = outcomeRepository.findOneNeedByUuid(cmdVM.getOutcomeViewModel().uuid);
        if (existing == null) {
            Outcome outcome = this.applyChanges(cmdVM);
            this.outcomeRepository.save(outcome);
        }
    }

    private Outcome applyChanges(OutcomeCloneCommandViewModel cmdVM) {
        return outcomeFromViewModel.apply(cmdVM.getOutcomeViewModel());
    }

    @Override
    protected ConfigCommand createCommand(Serializable targetId, Object params, String requestBody,
                                          OutcomeCloneCommandViewModel viewModel, long userId) {
        return new OutcomeCloneCommand(viewModel.uuid, viewModel.timestamp, userId, requestBody);
    }

}
