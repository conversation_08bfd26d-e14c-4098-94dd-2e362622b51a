import URI = require("URI");

import * as cmdDtos from "ecco-dto/command-dto";
import * as commands from "ecco-commands";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {Mergeable} from "ecco-dto";


export interface ReportDefChangeDto extends cmdDtos.UpdateCommandDto {
    reportDefUuid: string;

    name: cmdDtos.StringChangeOptional;

    friendlyName: cmdDtos.StringChangeOptional;

    orderby: cmdDtos.NumberChangeOptional;

    showOnDashboardManager: cmdDtos.BooleanChange;

    deleted?: cmdDtos.BooleanChange;

    definition: cmdDtos.StringChangeOptional;
}

/**
 * Command to add, remove or update
 */
export class ReportDefChangeCommand extends commands.BaseUpdateCommand {

    private nameChange: cmdDtos.StringChangeOptional;

    private friendlyNameChange: cmdDtos.StringChangeOptional;

    private definitionChange: cmdDtos.StringChangeOptional;

    private orderbyChange: cmdDtos.NumberChangeOptional;

    private showOnDashboardManagerChange: cmdDtos.BooleanChange;

    /** operation should be either "add", "update" or "delete" */
    constructor(private operation: string, private reportDefUuid: Uuid) {
        super(URI("reportDef/")
            .segmentCoded("command")
            .segmentCoded("")
            .toString());
    }

    public canMerge(candidate: Mergeable) {
        return false;
    }

    public merge(previousCommand: this): this {
        return null;
    }

    /** Add name change data, but only if to != from */
    public changeName(from: string, to: string) {
        this.nameChange = this.asStringChange(from, to);
        return this;
    }

    /** Add name change data, but only if to != from */
    public changeFriendlyName(from: string, to: string) {
        this.friendlyNameChange = this.asStringChange(from, to);
        return this;
    }

    public changeShowOnDashboardManager(from: boolean, to: boolean) {
        this.showOnDashboardManagerChange = this.asBooleanChange(from, to);
        return this;
    }

    /** Add report definition change data, but only if to != from */
    public changeDefinition(from: string, to: string) {
        this.definitionChange = this.asStringChange(from, to);
        return this;
    }

    /** Add orderby change data */
    public changeOrderby(from: number, to: number) {
        if (to != from) {
            this.orderbyChange = {from: from, to: to};
        }
        return this;
    }

    /** We can build this command by throwing possibly 'non-change' data at it.  This will return 'true' only
     *  if the result was that there are changes to submit */

    public hasChanges(): boolean {
        return this.nameChange != null || this.definitionChange != null || this.orderbyChange != null
            || this.friendlyNameChange != null
            || this.showOnDashboardManagerChange != null
            || this.operation == "add" || this.operation == "remove";
    }

    public toDto(): ReportDefChangeDto {
        return ({
                uuid: this.getUuid().toString(),
                commandUri: this.commandUri,
                timestamp: this.timestamp,
                operation: this.operation,
                reportDefUuid: this.reportDefUuid.toString(),
                name: this.nameChange,
                showOnDashboardManager: this.showOnDashboardManagerChange,
                friendlyName: this.friendlyNameChange,
                orderby: this.orderbyChange,
                definition: this.definitionChange
        });
    }
}
