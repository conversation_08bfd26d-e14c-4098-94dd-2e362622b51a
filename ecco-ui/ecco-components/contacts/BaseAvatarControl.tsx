import * as React from "react";
import {Component, createRef, RefObject} from "react";
import {ImageResizer} from "../files/ImageResizer";
import {WebApiError} from "@eccosolutions/ecco-common";
import {EccoAPI} from "../EccoAPI";
import {Individual} from "ecco-dto";

export interface AvatarProps {
    contact: Individual;
}

interface _Props extends AvatarProps {
    services: EccoAPI;
}

interface State {
    imageUrl?: string;
    mouseOver: boolean;
    errorText?: string;
    errorReason?: string;
}

export class BaseAvatarControl<PROPS extends _Props> extends Component<PROPS, State> {
    protected imageResizer: ImageResizer;

    protected avatarControllerUri: string;

    protected readonly disabled: boolean;

    protected fileInputRef: RefObject<HTMLInputElement>;

    constructor(props: PROPS) {
        super(props);

        this.state = {
            mouseOver: false,
            imageUrl: props.contact.avatarId ? this.imageUrl(props.contact.avatarId) : undefined
        };

        this.imageResizer = new ImageResizer({maxWidth: 300, maxHeight: 400});

        this.avatarControllerUri = `contact/${props.contact.contactId}/avatar/`;

        this.disabled = !this.imageResizer.supported() || !FormData;

        this.fileInputRef = createRef<HTMLInputElement>();
    }

    protected imageUrl(imageId: number) {
        return `${this.props.services.apiClient.getWebApiUrl()}secure/images/${imageId}`;
    }

    protected onClickOverlay(e: React.MouseEvent<HTMLDivElement>): boolean {
        e.preventDefault();
        e.stopPropagation();

        if (
            window.confirm(
                "Are you sure you wish to remove this photo? " +
                    "The photo will be deleted from the system: this operation " +
                    "cannot be undone."
            )
        ) {
            this.removeClientAvatar();
        }

        return false;
    }

    private removeClientAvatar(): void {
        this.props.services.apiClient
            .del(this.avatarControllerUri, "")
            .catch((reason: WebApiError) => {
                this.setState({
                    errorText: "Add failed - try again",
                    errorReason: reason.message || reason.statusCode.toString()
                });
            })
            .then((): void => {
                this.setState({imageUrl: undefined});
            });
    }

    protected onFileInputClick(): void {
        // Ensure that onFileChange is always called after the user selects a
        // file, even if they select the same file twice in a row.
        if (this.fileInputRef && this.fileInputRef.current) {
            this.fileInputRef.current.value = "";
        }
    }

    protected onFileChange(): void {
        const files = this.fileInputRef?.current?.files;

        if (!files) {
            throw new Error("Illegal state.");
        } else if (files.length == 0) {
            this.removeClientAvatar();
        } else if (files.length > 1) {
            throw new Error("Illegal state.");
        } else {
            const file = files[0];

            const resizedFile = this.imageResizer.resizeFile(file);

            const formData = resizedFile
                .then(resizedFile => {
                    const formData = new FormData();
                    formData.append("file", resizedFile, file.name);
                    return formData;
                })
                .catch(reason => {
                    this.setState({errorText: "Resize failed - try again", errorReason: reason});
                });

            // avatar is a global upload
            formData.then(formData => {
                this.props.services.apiClient
                    .post<UploadedFileOrErrorResultDto>(
                        "secure/uploadHiddenJs.html?source=global",
                        formData!,
                        undefined
                    )
                    .then(data => {
                        if (data.error == null) {
                            this.setClientAvatar(data);
                        } else {
                            this.setState({
                                errorText: "Add errored - try again",
                                errorReason: data.error
                            });
                        }
                    })
                    .catch((reason: WebApiError) => {
                        this.setState({
                            errorText: "Add errored - try again",
                            errorReason: reason.message || reason.statusCode.toString()
                        });
                    });
            });
        }
    }

    private setClientAvatar(file: UploadedFileDto): void {
        this.props.services.apiClient
            .post(this.avatarControllerUri, null, undefined, {
                query: {bytesId: file.bytesId!.toString()}
            })
            .catch((reason: WebApiError) => {
                this.setState({
                    errorText: "Add failed - try again",
                    errorReason: reason.message || reason.statusCode.toString()
                });
            })
            .then((): void => {
                this.setState({imageUrl: this.imageUrl(file.bytesId!)});
            });
    }
}

/** Matches UploadedFileResource.
 *
 * All fields are marked optional as the Web API may return
 * UploadErrorResultDto instead. */
interface UploadedFileDto {
    links?: LinkDto[];
    fileId?: number;
    filename?: string;
    size?: number;
    bytesId?: number;
    type?: string;
}

/** Matches org.springframework.hateoas.Link. */
interface LinkDto {
    href: string;
    rel: string;
}

/** Matches output of UploadErrorResult#toJson().
 *
 * All fields are marked optional as the Web API may return
 * UploadedFileDto instead. */
interface UploadErrorResultDto {
    filename?: string;
    type?: string;
    error?: string;
}

/** Either UploadedFileDto or UploadErrorResultDto. */
interface UploadedFileOrErrorResultDto extends UploadedFileDto, UploadErrorResultDto {}
