package com.ecco.infrastructure.rest.hateoas.schema;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.module.jsonSchema.types.LinkDescriptionObject;

/**
 * Overrides the serialization behaviour of {@link com.fasterxml.jackson.module.jsonSchema.types.LinkDescriptionObject}
 * which duplicates the schema onto two properties ({@code jsonSchema} and {@code schema}) when serializing.
 * The correct output property is {@code schema}.
 *
 * @since 22/01/2016
 */
@JsonIgnoreProperties("jsonSchema")
public class SchemaProvidingLinkDescriptionObject extends LinkDescriptionObject {
}
