<%@page isErrorPage="true" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" trimDirectiveWhitespaces="true"%>

<%@include file="/WEB-INF/views/pathvars.jsp" %>

<%@taglib prefix="ecco" tagdir="/WEB-INF/tags"%>

<%--NOTE REFERRAL SPECIFIC BITS for image --%>
<ecco:main_dev classes="new-layout" title="${titleRaw}" contentFragPath="views/content/${content}" devMode="${devMode}">
    <jsp:attribute name="headFrag">
        <%@ include file="/WEB-INF/views/tiles/main_head_js.jsp" %> <%--includes /WEB-INF/views/heads/${head}.jsp --%>
    </jsp:attribute>
    <jsp:attribute name="menuFrag">
        <c:if test="${not empty menu}">
            <c:import url="/WEB-INF/views/menus/${menu}.jsp"/>
        </c:if>
    </jsp:attribute>
    <jsp:attribute name="actionbarFrag">
        <c:if test="${not empty actionbar}">
            <div class="clearfix rounded actionbar">
                <c:import url="/WEB-INF/views/content/${actionbar}.jsp"/>
            </div>
        </c:if>
    </jsp:attribute>
    <jsp:attribute name="sidebarFrag">
        <c:if test="${not empty context}">
            <c:import url="/WEB-INF/views/contexts/${context}.jsp"/>
        </c:if>
    </jsp:attribute>
    <jsp:body>
        <div class="row">
            <div class="col-xs-12 rounded text-center" style="padding-top: 15px">
                <c:import url="/WEB-INF/views/content/${content}.jsp"/>
            </div>
        </div>
    </jsp:body>
</ecco:main_dev>

