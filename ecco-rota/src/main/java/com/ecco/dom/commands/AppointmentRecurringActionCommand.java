package com.ecco.dom.commands;

import com.ecco.dom.servicerecipients.ServiceRecipientCommand;
import org.joda.time.Instant;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import java.util.UUID;

@Entity
@DiscriminatorValue("rotaApptActionRec")
public class AppointmentRecurringActionCommand extends ServiceRecipientCommand {

    @Nullable
    @Column
    protected Long scheduleId;

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    public AppointmentRecurringActionCommand() {
        super();
    }

    public AppointmentRecurringActionCommand(@Nullable UUID uuid, @Nonnull Instant remoteCreationTime,
                                             long userId, @Nonnull String body, int serviceRecipientId,
                                             long scheduleId) {
        super(uuid, remoteCreationTime, userId, body, serviceRecipientId);
        this.scheduleId = scheduleId;
    }

}
