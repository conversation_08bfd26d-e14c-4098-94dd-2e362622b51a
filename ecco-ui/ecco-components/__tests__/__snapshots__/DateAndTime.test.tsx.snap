// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`DateAndTime should render correctly 1`] = `
<FormGroup
  bsClass="form-group"
  label="date/time"
  validationState="success"
>
  <label
    className="control-label has-success"
  >
    date/time
  </label>
  <div>
    <div
      style={
        {
          "display": "inline-block",
          "width": "50%",
        }
      }
    >
      <FormControl
        bsClass="form-control"
        componentClass="input"
        disabled={false}
        name="instant_date"
        onChange={[Function]}
        type="date"
        value=""
      />
    </div>
    <div
      style={
        {
          "display": "inline-block",
          "width": "50%",
        }
      }
    >
      <FormControl
        bsClass="form-control"
        componentClass="input"
        disabled={false}
        name="instant_time"
        onChange={[Function]}
        type="time"
        value=""
      />
    </div>
  </div>
</FormGroup>
`;

exports[`DateAndTime should return null when I set only date 1`] = `
<DateAndTime
  disabled={false}
  label="date/time"
  propertyKey="instant"
  required={true}
  state={
    {
      "instant": "2018-12-13T12:23:00",
    }
  }
  stateSetter={[Function]}
>
  <FormGroup
    bsClass="form-group"
    label="date/time"
    validationState="success"
  >
    <div
      className="form-group has-success"
      label="date/time"
    >
      <label
        className="control-label has-success"
      >
        date/time
      </label>
      <div>
        <div
          style={
            {
              "display": "inline-block",
              "width": "50%",
            }
          }
        >
          <FormControl
            bsClass="form-control"
            componentClass="input"
            disabled={false}
            name="instant_date"
            onChange={[Function]}
            type="date"
            value="12/05/2015"
          >
            <input
              className="form-control"
              disabled={false}
              name="instant_date"
              onChange={[Function]}
              type="date"
              value="12/05/2015"
            />
          </FormControl>
        </div>
        <div
          style={
            {
              "display": "inline-block",
              "width": "50%",
            }
          }
        >
          <FormControl
            bsClass="form-control"
            componentClass="input"
            disabled={false}
            name="instant_time"
            onChange={[Function]}
            type="time"
            value=""
          >
            <input
              className="form-control"
              disabled={false}
              name="instant_time"
              onChange={[Function]}
              type="time"
              value=""
            />
          </FormControl>
        </div>
      </div>
    </div>
  </FormGroup>
</DateAndTime>
`;

exports[`DateAndTime typing should set date/time 1`] = `
<DateAndTime
  disabled={false}
  label="date/time"
  propertyKey="instant"
  required={true}
  state={
    {
      "instant": "2018-12-13T12:23:00",
    }
  }
  stateSetter={[Function]}
>
  <FormGroup
    bsClass="form-group"
    label="date/time"
    validationState="success"
  >
    <div
      className="form-group has-success"
      label="date/time"
    >
      <label
        className="control-label has-success"
      >
        date/time
      </label>
      <div>
        <div
          style={
            {
              "display": "inline-block",
              "width": "50%",
            }
          }
        >
          <FormControl
            bsClass="form-control"
            componentClass="input"
            disabled={false}
            name="instant_date"
            onChange={[Function]}
            type="date"
            value="2015-05-12"
          >
            <input
              className="form-control"
              disabled={false}
              name="instant_date"
              onChange={[Function]}
              type="date"
              value="2015-05-12"
            />
          </FormControl>
        </div>
        <div
          style={
            {
              "display": "inline-block",
              "width": "50%",
            }
          }
        >
          <FormControl
            bsClass="form-control"
            componentClass="input"
            disabled={false}
            name="instant_time"
            onChange={[Function]}
            type="time"
            value="12:18"
          >
            <input
              className="form-control"
              disabled={false}
              name="instant_time"
              onChange={[Function]}
              type="time"
              value="12:18"
            />
          </FormControl>
        </div>
      </div>
    </div>
  </FormGroup>
</DateAndTime>
`;
