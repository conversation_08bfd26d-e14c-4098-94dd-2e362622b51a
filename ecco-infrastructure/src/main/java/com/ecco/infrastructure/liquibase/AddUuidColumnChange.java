package com.ecco.infrastructure.liquibase;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.UUID;

/**
 * Introducing a UUID column requires the column be populated, but this will need to be done on the basis of what
 * the current key is that the UUID will replace/enhance.
 */
public class AddUuidColumnChange extends UpdateableResultSetChange {

    private String newUuidColumn;

    private String uniqueKeyColumns;

    private String lastUniqueKey = null;
    private UUID currentUuid = UUID.randomUUID();

    public String getNewUuidColumn() {
        return newUuidColumn;
    }

    public void setNewUuidColumn(String newColumnName) {
        this.newUuidColumn = newColumnName;
    }

    public String getUniqueKeyColumns() {
        return uniqueKeyColumns;
    }

    /** Comma sep list of column names that determine what will have the same UUID */
    public void setUniqueKeyColumns(String uniqueKeyColumns) {
        this.uniqueKeyColumns = uniqueKeyColumns;
    }

    @Override
    protected String getOrderSql() {
        return "ORDER BY " + getUniqueKeyColumns();
    }

    @Override
    protected void computeChange(ResultSet rs) throws SQLException {

        String primaryKey = computeUniqueKey(rs);

        // If change of key
        if (!primaryKey.equals(lastUniqueKey)) {
            currentUuid = UUID.randomUUID();
        }
        lastUniqueKey = primaryKey;

        rs.updateString(newUuidColumn, currentUuid.toString());
    }

    private String computeUniqueKey(ResultSet rs) throws SQLException {
        StringBuilder key = new StringBuilder();

        for (String field : getUniqueKeyColumns().split(",")) {
            key.append(rs.getObject(field).toString()).append(":");
        }
        return key.toString();
    }

    @Override
    protected String getAdditionalColumns() {
        return getUniqueKeyColumns() + "," + getNewUuidColumn();
    }
}
