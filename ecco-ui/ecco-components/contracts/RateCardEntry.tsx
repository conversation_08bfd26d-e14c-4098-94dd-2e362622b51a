import {Nullable, SelectListOption} from "@eccosolutions/ecco-common";
import * as React from "react";

import {RateCardDto, RateCardEntryDto} from "ecco-dto";
import {Box, Grid} from "@eccosolutions/ecco-mui";
import {checkBox, dropdownList, numberInput} from "../MUIComponentUtils";
import {useServicesContext} from "../ServicesContext";


interface Props {
    rateCard: RateCardDto
    rateCardEntry: Nullable<RateCardEntryDto>;
    chargeTypes: SelectListOption[];
    onChangeRateCardEntry: (rateCardEntry: Partial<Nullable<RateCardEntryDto>>) => void;
}

export function RateCardEntry(props: Props) {
    const { rateCardEntry, onChangeRateCardEntry, chargeTypes } = props;

    const {sessionData} = useServicesContext();

    const updatePartial = (update: Partial<Nullable<RateCardEntryDto>>) => {
        onChangeRateCardEntry({...rateCardEntry, ...update});
    };

    return (
        <Box m={4}>
            <Grid container direction="row" justify="flex-start" alignItems="flex-start">
                <Grid container>
                    <Grid item xs={12}>
                        {dropdownList("outcome", onChangeRateCardEntry, rateCardEntry, "matchingOutcomeId",
                            sessionData?.getListDefinitionEntriesByListName("eventStatusRateId").map(l => l.getDto()) || [],undefined, undefined, true)
                        }
                    </Grid>
                    <Grid item xs={12}>
                        {dropdownList("type", onChangeRateCardEntry, rateCardEntry, "chargeType",
                            chargeTypes,undefined, undefined, true)
                        }
                    </Grid>
                    {(rateCardEntry.chargeType == "TEMPORAL" || rateCardEntry.chargeType == "FIXED_TEMPORAL")
                    &&
                    <>
                        <Grid item xs={12}>
                            {numberInput("unitCharge", "unit charge", newState => updatePartial({unitCharge: newState.unitCharge}), rateCardEntry, undefined, 2)}
                        </Grid>
                        <Grid item xs={12}>
                        {numberInput("units", "minutes", newState => updatePartial({units: newState.units}), rateCardEntry, undefined)}
                        </Grid>
                    </>
                    }
                    {(rateCardEntry.chargeType == "FIXED" || rateCardEntry.chargeType == "FIXED_TEMPORAL")
                    &&
                    <>
                        <Grid item xs={12}>
                            {numberInput("fixedCharge", "fixed charge", newState => updatePartial({fixedCharge: newState.fixedCharge}), rateCardEntry, undefined, 2)}
                        </Grid>
                    </>
                    }
                    <Grid item xs={12}>
                        {checkBox("defaultEntry", "default", newState => updatePartial({defaultEntry: newState.defaultEntry}), rateCardEntry)}
                    </Grid>
                    <Grid item xs={12}>
                        {checkBox("disabled", "disabled", newState => updatePartial({disabled: newState.disabled}), rateCardEntry)}
                    </Grid>
                </Grid>
            </Grid>
        </Box>
    );
}
