package com.ecco.webApi.evidence

import com.ecco.dao.ReferralRepository
import com.ecco.dom.ReferralServiceRecipient
import com.ecco.dom.incidents.IncidentServiceRecipient
import com.ecco.evidence.repositories.ServiceRecipientRepository
import com.ecco.serviceConfig.EntityRestrictionService
import com.ecco.serviceConfig.repositories.ServiceCategorisationRepository
import com.ecco.serviceConfig.service.RepositoryBasedServiceCategorisationService
import com.ecco.serviceConfig.service.ServiceTypeService
import com.ecco.servicerecipient.ServiceRecipientSummary
import com.ecco.servicerecipient.ServiceRecipientSummaryService
import com.ecco.webApi.controllers.BaseWebApiController
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.util.Assert
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RestController

@RestController
class ServiceRecipientBaseController @Autowired constructor(
    protected val serviceRecipientRepository: ServiceRecipientRepository,
    private val serviceRecipientSummaryService: ServiceRecipientSummaryService,
    // Temporary - we hope!  Used as short cut when it's a referral (which it usually is)
    private val referralRepository: ReferralRepository,
    private val entityRestrictionService: EntityRestrictionService,
    private val serviceCategorisationRepository: RepositoryBasedServiceCategorisationService
) : BaseWebApiController() {
    private val referralSummaryToViewModel: ReferralSummaryToViewModel

    init {
        referralSummaryToViewModel = ReferralSummaryToViewModel()
    }

    fun findOneWithoutAclSecurity(@PathVariable serviceRecipientId: Int): ServiceRecipientSummary {
        return findOne(serviceRecipientId, cached = false, withoutAclSecurity = true);
    }

    // called by service-recipient/{serviceRecipientId}
    fun findOne(serviceRecipientId: Int, cached: Boolean = false, withoutAclSecurity: Boolean = false): ServiceRecipientSummary {

        if (cached) {
            // load a cached version, but this appears unused, as it's only called by 'findOneCached' which has no client-side end point
            val sr = serviceRecipientSummaryService.findOne(serviceRecipientId)
            if (!withoutAclSecurity) {
                EntityRestrictionService.verifyAccess(sr.serviceRecipientId, sr.serviceIdAcl, sr.projectIdAcl, entityRestrictionService, serviceCategorisationRepository)
            }
            return sr
        }

        // This is optimised for a referral - and faster - we hope than the other approach.
        val referralSummary = referralRepository.findOneReferralSummaryByServiceRecipientId(serviceRecipientId)
        if (referralSummary != null) {
            if (!withoutAclSecurity) {
                EntityRestrictionService.verifyAccess(referralSummary.serviceRecipientId, referralSummary.serviceIdAcl, referralSummary.projectIdAcl, entityRestrictionService, serviceCategorisationRepository)
            }
            return referralSummaryToViewModel.apply(referralSummary)
        }
        val recipient = serviceRecipientRepository.findById(serviceRecipientId).orElseThrow()
        if (recipient.prefix == IncidentServiceRecipient.PREFIX) {
            // if we have the incident's service/project permission (not the 'incidents' service)
            EntityRestrictionService.verifyAccess(
                recipient.id,
                recipient.permissionServiceId,
                recipient.permissionProjectId,
                entityRestrictionService,
                serviceCategorisationRepository
            )
            // TODO also verify if we are a manager OR have 'incident' permission
        }

        Assert.isTrue(recipient !is ReferralServiceRecipient, "")
        return ServiceRecipientSummary().mapFrom(recipient);
    }

}