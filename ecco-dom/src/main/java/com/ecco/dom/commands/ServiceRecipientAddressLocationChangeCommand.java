package com.ecco.dom.commands;

import com.ecco.dom.servicerecipients.ServiceRecipientCommand;
import org.joda.time.Instant;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.persistence.*;
import java.util.UUID;

/**
 * Commands for a contact's addressLocation.
 */
@Entity
@DiscriminatorValue("addressLoc")
public class ServiceRecipientAddressLocationChangeCommand extends ServiceRecipientCommand {

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    public ServiceRecipientAddressLocationChangeCommand() {
        super();
    }

    public ServiceRecipientAddressLocationChangeCommand(@Nullable UUID uuid, @Nonnull Instant remoteCreationTime,
                                                        long userId, @Nonnull String body, int serviceRecipientId) {
        super(uuid, remoteCreationTime, userId, body, serviceRecipientId);
    }

}
