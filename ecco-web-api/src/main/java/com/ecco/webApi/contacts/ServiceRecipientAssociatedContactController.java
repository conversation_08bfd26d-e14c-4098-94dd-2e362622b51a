package com.ecco.webApi.contacts;

import com.ecco.dao.ServiceRecipientContactRepository;
import com.ecco.dao.ReferralRepository;
import com.ecco.dom.ServiceRecipientContact;
import com.ecco.security.repositories.ContactRepository;
import com.ecco.webApi.controllers.BaseWebApiController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Nonnull;
import java.util.List;
import java.util.stream.Collectors;

@RestController
public class ServiceRecipientAssociatedContactController extends BaseWebApiController {

    @Nonnull private final ServiceRecipientContactRepository serviceRecipientContactRepository;
    @Nonnull private final ContactRepository contactRepository;

    @Autowired
    public ServiceRecipientAssociatedContactController(@Nonnull ContactRepository contactRepository,
                                                       @Nonnull ServiceRecipientContactRepository serviceRecipientContactRepository) {
        this.contactRepository = contactRepository;
        this.serviceRecipientContactRepository = serviceRecipientContactRepository;
    }

    @PreAuthorize("hasAnyRole('ROLE_STAFF')")
    @GetJson(value = "/servicerecipient/{serviceRecipientId}/contacts/")
    public Iterable<ServiceRecipientAssociatedContactViewModel> findContacts(@PathVariable int serviceRecipientId) {

        ServiceRecipientAssociatedContactToViewModel toViewModel = new ServiceRecipientAssociatedContactToViewModel(this.contactRepository, this.commandRepository);
        List<ServiceRecipientContact> contacts = this.serviceRecipientContactRepository.findAllById_ServiceRecipientId(serviceRecipientId);

        return contacts.stream().map(toViewModel).collect(Collectors.toList());
    }

}
