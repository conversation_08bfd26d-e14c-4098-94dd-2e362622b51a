package com.ecco.testsupport;

import com.ecco.config.dom.Setting;
import com.ecco.config.service.SettingsService;
import com.ecco.config.service.SoftwareModuleService;
import com.ecco.dom.Individual;
import com.ecco.infrastructure.bus.MessageBus;
import com.ecco.infrastructure.config.root.*;
import com.ecco.security.ReferenceDataImpl;
import com.ecco.security.dom.User;
import com.ecco.security.repositories.GroupRepository;
import com.ecco.security.service.UserManagementService;
import com.ecco.security.service.UserManagementServiceImpl;
import com.ecco.test.support.SpringTestSupport;
import com.ecco.web.nav.MfaController;
import com.ecco.security.ReferenceDataSource;
import com.ecco.webapp.config.root.SecurityConfig;
import org.junit.Before;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.security.crypto.password.MessageDigestPasswordEncoder;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.servlet.Filter;
import java.util.Set;

import static org.springframework.security.test.web.servlet.setup.SecurityMockMvcConfigurers.springSecurity;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;

/**
 * A class to test security aspects of the application - without spring-boot. For spring-boot, see ApplicationTests.
 * Similar to AbstractSecurityIntegrationTest (in ecco-security), but we want to use EccoApplicationContextInitializer
 * for as much full infrastructure context as possible, therefore we need to be in the parent (med-security). However,
 * we need a SecurityConfigurer (applicationContext-security.xml) so we are in ecco-war to use the LegacyConfig.class.
 * Otherwise we could be in med-security and use WebSecurityConfigurer.class.
 * By including a SecurityConfigurer and a few more annotations, we can use spring security tests against relevant config
 * that the actual web.xml uses. We could also use spring-boot to also make use of their annotations also, but we don't want
 * to confuse ourselves by including it in the pom.
 *      NB Annotations like @WebMvcTest = @AutoConfigureWebMvc (web layer) + @AutoConfigureMockMvc (for MockMvc)
 * TODO include other aspects from web.xml - such as CookieConfigServletContextListener / tracking-mode>COOKIE / exception views etc
 * TODO use SecurityDomainAppContextInitializer over EccoApplicationContextInitializer (pre-configured for security)
 * TODO we could merge ecco-security into med-security.
 *
 * Based on examples from spring blog on security:
 *      Part 1 - method security with WithSecurity...Listener: https://spring.io/blog/2014/05/07/preview-spring-security-test-method-security.
 *      Part 2 - web security: https://spring.io/blog/2014/05/23/preview-spring-security-test-web-security
 *      https://github.com/rwinch/spring-security-test-blog/blob/master/src/test/java/org/springframework/security/test/web/servlet/showcase/secured/WithUserAuthenticationTests.java
 */

// NB create an application context
@RunWith(SpringJUnit4ClassRunner.class)
@ActiveProfiles({Profiles.EMBEDDED, Profiles.TEST_FIXTURE})
// NB would be @SpringApplicationConfiguration in spring - to use spring-boot's SpringApplicationContextListener
//@ContextConfiguration(
        // TODO don't initilise liqibase for security tests??
//        initializers=EccoApplicationContextInitializer.class, // or SecurityDomainAppContextInitializer
        // as per web.xml contextConfigLocation for now, since we can strip back later
        //classes = {RootConfig.class, ServletRootConfig.class}
//        classes = {RootConfig.class, ServletRootConfig.class}
        // NB don't use a loader else we don't get a WebApplicationContext - see https://stackoverflow.com/questions/57536723/no-qualifying-bean-of-type-org-springframework-web-context-webapplicationconte
        /*loader = StackTraceSanitisingContextLoader.class*/
//        )

@ContextConfiguration(initializers = AbstractWebSecurityTest.SecurityDomainAppContextInitializer.class,
        classes={ InfrastructureConfig.class, SecurityConfig.class, AbstractWebSecurityTest.ServiceConfig.class})

//        classes={InfrastructureConfig.class,
//                AbstractWebSecurityTest.ServiceConfig.class}

// NB indicate we need a Spring WebApplicationContext
//      ? does this pick up web.xml
@WebAppConfiguration

// NB default listeners are registered for us with MockMvc
//@TestExecutionListeners(listeners={ServletTestExecutionListener.class,
//        DependencyInjectionTestExecutionListener.class,
//        DirtiesContextTestExecutionListener.class,
//        TransactionalTestExecutionListener.class,
//        // NB With...Listener is how spring security hooks into Spring Test - providing a wrap around the SecurityHolder
//        WithSecurityContextTestExecutionListener.class})

// NB test spring mvc requires filters, so we need to supply a security configurer because we can't create DelegatingFilterProxy if MockHttpServletRequest doesn't implement the required getFilters
// so we need to include applicationContext-security - either via the newer java config (WebSecurityConfigurer.class) or legacy (LegacyConfig.class ...-web-common)
// NB Filter#init(ServletConfig) is a container managed method, whereas mvc tests only test the MVC configuration (@Controller and other mappings)
//@Import({LegacyConfig.class, UTCConfig.class})




// The Spring MVC Test suite is not meant to test the container configuration, it is meant to test your MVC (@Controller and other mappings) configuration . Filter#init(ServletConfig) is a container managed method.
// and
// For a Spring Boot app, if @SpringBootTest(webEnvironment = WebEnvironment.RANDOM_PORT) is used then filter.init() is called automatically.
// https://stackoverflow.com/questions/20819130/whats-required-to-make-mockmvc-test-a-filters-init-routine

// we get a hint in https://spring.io/blog/2013/07/03/spring-security-java-config-preview-web-security/
// that we need to configure

// app-based TOTP: https://golb.hplar.ch/2019/06/totp-spring-security.html#totp / https://golb.hplar.ch/2019/05/stateless.html
// website-based TOTP: extra login form parameter: https://www.baeldung.com/spring-security-two-factor-authentication-with-soft-token
//                                                 https://www.javadevjournal.com/spring-security/two-factor-authentication-with-spring-security/
//      BUT we'd prefer to use a filter, unless we perhaps use a global setting to enforce TOTP
// spring-security: MFA 'issue' https://github.com/spring-projects/spring-security/issues/2603

// @WithUserDetails - to supply a custom principal type, assuming 'userDetailsService' is a bean it will be invoked
// however... we can provide the user also in its own annotation @WithSecurityContext

// DO .perform(get("/").with(httpBasic("user","password")))
// DO .perform(formLogin("/auth").user("admin").password("pass"))
//      .perform(formLogin().password("invalid")).andExpect(unauthenticated());
// DO .perform(logout("/signout"))

// FILTER tests
// Basic https://github.com/spring-projects/spring-security/blob/main/web/src/test/java/org/springframework/security/web/authentication/www/BasicAuthenticationFilterTests.java
// new filer: https://stackoverflow.com/questions/70015407/spring-filter-chain-is-not-applied-on-unit-test-mockmvc
// .addFilters(filter)

// IMPL
// WebAuthnConfigurer (configure) - https://github.com/rwinch/spring-security-webauthn/blob/8dde03044ae183ba6b347c9ed01fc0bf4292ec13/src/main/java/example/webauthn/security/config/WebAuthnConfigurer.java#L56
// WebAuthnLoginFilter - https://github.com/rwinch/spring-security-webauthn/blob/master/src/main/java/org/springframework/security/web/webauthn/WebAuthnLoginFilter.java#L37-L56
// Mfa authenticator - https://github.com/rwinch/spring-security-webauthn/blob/8dde03044ae183ba6b347c9ed01fc0bf4292ec13/src/main/java/example/webauthn/Mfa.java
// baeldung https://www.baeldung.com/spring-delegating-filter-proxy

// FROM ??
    /*@Test
    public void optionsResponse_hasCorrectFilters() throws Exception {

        RequestBuilder requestBuilder = MockMvcRequestBuilders.options("/DataCustodian/oauth/token")
                .header("Origin", "foobar")
                .header("Access-Control-Allow-Origin", "*");

        MvcResult result = mvc.perform(requestBuilder)
                .andExpect(header().string("Access-Control-Allow-Origin", is("*")))
                .andExpect(header().string("Access-Control-Allow-Methods", is("GET, POST, PUT, DELETE, OPTIONS")))
                .andExpect(header().string("Access-Control-Allow-Headers", is("origin, authorization, accept, content-type")))
                .andExpect(header().string("Access-Control-Max-Age", is("1800")))
                .andReturn();
    }*/

@TestPropertySource(properties = {
        "debug true",
        //"liquibase DISABLED",
        "spring.jpa.properties.javax.persistence.validation.mode none",
        //"azure.activedirectory.client-id something-to-enable-autoconfig"
})
abstract public class AbstractWebSecurityTest {

    // TODO this is duplication of @TestPropertySource
    @Configuration
    public static class SecurityDomainAppContextInitializer extends EccoApplicationContextInitializer {
        protected void configureEnvironment(ConfigurableEccoEnvironment environment) {
            //environment.setActiveProfiles(Profiles.EMBEDDED);
            SpringTestSupport.configureMockPropertySource(environment)
//                    .withProperty("db.embedded-persist-path", "./target/h2/AbstractWebSecurityTest")
                    .withProperty("hibernate.hbm2ddl", "none")
                    .withProperty("db.version", SchemaVersion.V1_1);
        }
/*
        @Override
        protected void configureEnvironment(ConfigurableEccoEnvironment environment) {
            environment.setActiveProfiles(Profiles.EMBEDDED);
            SpringTestSupport.configureMockPropertySource(environment)
                    .withProperty("liquibase", LiquibaseMode.DISABLED.toString())
                    .withProperty("hibernate.hbm2ddl", "none");
        }
*/

    }
    @Configuration
    //@EnableWebSecurity
    // as per AbstractSecurityIntegrationTest and SecurityConfig
    public static class ServiceConfig {

        @PersistenceContext
        EntityManager entityManager;

        @Bean public MessageDigestPasswordEncoder passwordEncoder() {
            return new MessageDigestPasswordEncoder("SHA-1");
        }

        // NB MessageSourceConfig is not included in infrastructure, but we lose the authentication exception with a NoSuchMessageException exception
        @Bean
        public MessageSource messageSource() {
            return Mockito.mock(MessageSource.class);
        }

        @Bean
        public MessageSourceAccessor messageSourceAccessor() {
            return Mockito.mock(MessageSourceAccessor.class);
        }

        @Bean
        public ReferenceDataImpl applicationReferenceData() { return Mockito.mock(ReferenceDataImpl.class); }


        @Bean public SettingsService settingsService() {
            var service = Mockito.mock(SettingsService.class);
            var setting = Mockito.mock(Setting.class);
            Mockito.when(service.settingFor(Mockito.anyString(), Mockito.anyString())).thenReturn(setting);
            Mockito.when(setting.getValue()).thenReturn("10");
            return service;
        }

        @Bean public SoftwareModuleService softwareModuleService() {
            return Mockito.mock(SoftwareModuleService.class);
        }

        @Bean public UserManagementServiceImpl userManagementService(SettingsService settingsService, MessageBus<ApplicationEvent> messageBus) {
            return new UserManagementServiceImpl(passwordEncoder(), entityManager, messageBus,
                    settingsService, Mockito.mock(GroupRepository.class), softwareModuleService());
        }

        // NB when spring boot starts (ecco-webapi-boot) it sets ecco.mvc.basePath as /nav
        //  which now captures the MfaController mapping to /nav/mfa/validate.
        //  However, as default, the test have no such prefix and expect /mfa/validate
        //  and hence the MfaWebSecurityTest fails as we've moved the MfaController to be picked up by spring boot /nav.
        //  We could consider ecco.mvc.basePath, or re-mapping - search for 'SimpleUrlHandlerMapping getHandlerMapping'
        //  or prefixing the controller inside mockMvc, or setting a servlet path
        //  but all these are probably best waiting for removed non spring boot, and setting paths correctly.
        @Bean
        public MfaController mfaController(UserManagementService userManagementService,
                                           @Qualifier("webSuccessHandler") AuthenticationSuccessHandler successHandler,
                                           ReferenceDataSource referenceData) {
            return new MfaController(userManagementService, successHandler, referenceData);
        }

    }
/*
    @Configuration
    //@Import(MfaController.class)
    //@ComponentScan
    public class ControllerConfig {

    }*/

    @Autowired
    protected WebApplicationContext context;

    @Autowired
    private UserManagementService userManagementService;

    @Autowired
    protected Filter springSecurityFilterChain;

    protected MockMvc mockMvc;

    @Before
    public void setup() {
        mockMvc = MockMvcBuilders
                .webAppContextSetup(context)
                //.addFilter()
                // NB springSecurity() provides all the setup between spring security and spring mvc
                .apply(springSecurity())
                .defaultRequest(get("/"))
                //        .servletPath("/nav"))
        // NB testSecurityContext() is already provided in springSecurity() - see javadoc
                    //.with(testSecurityContext()))
                .build();
    }

    protected void createUser(String username, String password, boolean mfaRequired, String mfaSecret, String... groups) {
        Individual.Builder b = Individual.builder(username, "user")
                .withNewUser(username, password)
                .withNewGroups(Set.of(groups))
                .withMfaRequired(mfaRequired)
                .withMfaSecret(mfaSecret);
        User user = b.build().getUser();
        userManagementService.createUser(user);
    }

    // FROM https://github.com/rwinch/spring-security-test-blog/blob/master/src/test/java/org/springframework/security/test/web/servlet/showcase/secured/WithUserAuthenticationTests.java
    /*
    // NB WithMockUser provides a username: 'user'/'password' and ROLE_USER for UsernamePasswordAuthenticationToken
    @Test
    @WithMockUser
    public void requestProtectedUrlWithUser() throws Exception {
        mvc
                .perform(get("/"))
                // Ensure we got past Security
                .andExpect(status().isNotFound())
                // Ensure it appears we are authenticated with user
                .andExpect(authenticated().withUsername("user"));
    }

    @Test
    @WithMockUser(roles="ADMIN")
    public void requestProtectedUrlWithAdmin() throws Exception {
        mvc
                .perform(get("/admin"))
                // Response status is 'not found' because we have no controller, but we got past spring security
                .andExpect(status().isNotFound())
                // Ensure it appears we are authenticated with user
                .andExpect(authenticated().withUsername("user").withRoles("ADMIN"));
    }

    @Configuration
    @EnableWebMvcSecurity
    @EnableWebMvc
    static class Config extends WebSecurityConfigurerAdapter {
        @Override
        protected void configure(HttpSecurity http) throws Exception {
            http
                    .authorizeRequests()
                    .antMatchers("/admin/**").hasRole("ADMIN")
                    .anyRequest().authenticated()
                    .and()
                    .formLogin();
        }

        // FROM blog
        @Autowired
        public void configureGlobal(AuthenticationManagerBuilder auth) throws Exception {
            auth
                    .inMemoryAuthentication()
                    .withUser("user").password("password").roles("USER");
        }
    }
    */
}
