import _ = require("lodash");
import * as React from "react"
import {apiClient} from "ecco-components";
import {ServiceAjaxRepository, ServiceDto, SessionDataGlobal} from "ecco-dto";
import {EntityRestrictionsAjaxRepository} from "../entity-restrictions/EntityRestrictionsAjaxRepository";

interface Props extends React.ClassAttributes<ChooseService> {
    /**
     * Callback when selected
     * @param service the service dto
     * @param hasAccessPermission true if present in the restricted servicesProjects list
     */
    onChange: (service: ServiceDto, hasAccessPermission: boolean) => void;
    allowInboundServices: boolean;
}

interface State {
    serviceIndex: Record<string, ServiceDto>;
    serviceInboundIndex: Record<string, ServiceDto>;
    serviceAllIndex: Record<string, ServiceDto>;
}

export class ChooseService extends React.Component<Props, State> {

    private entityRestrictionsRepository: EntityRestrictionsAjaxRepository = new EntityRestrictionsAjaxRepository(apiClient);
    private serviceRepository: ServiceAjaxRepository = new ServiceAjaxRepository(apiClient);

    constructor(props) {
        super(props);
        this.state = {
            serviceIndex: {},
            serviceAllIndex: {},
            serviceInboundIndex: {}
        };
    }

    public componentDidMount() {
        const servicesInboundQ: Promise<ServiceDto[]> = this.props.allowInboundServices
            ? this.serviceRepository.findAllServices().then(servicesAll => {
                return servicesAll.getDtos().filter(s => s.parameters && s.parameters.allowInboundReferrals);
            })
            : Promise.resolve([]);

        this.entityRestrictionsRepository.findRestrictedServicesProjects(true, true).then(services => {
            servicesInboundQ.then(servicesInbound => {
                const allServices = services.concat(servicesInbound);
                this.setState({
                    serviceIndex: _.keyBy(services, 'id'),
                    serviceInboundIndex: _.keyBy(servicesInbound, 'id'),
                    serviceAllIndex: _.keyBy(_.uniqBy(allServices, 'id'), 'id')
                });
            });
        });
    }

    private handleServiceClick = (serviceId: number) => {
        if (this.state.serviceIndex[serviceId]) {
            this.props.onChange(this.state.serviceIndex[serviceId], true);
        } else {
            this.props.onChange(this.state.serviceInboundIndex[serviceId], false);
        }
    };

    render() {
        return (
            <div className='row ecco-rounded'>
                <div className='col-xs-10 col-xs-offset-1 text-center'>
                    <p>which service has been requested?</p>
                    <ul className='list-unstyled double-spaced'>
                        {Object.values(this.state.serviceAllIndex)
                            .sort(SessionDataGlobal.compare)
                            .map(service => (
                            <li
                                key={service.id}>
                                <a
                                    className="btn btn-link"
                                    onClick={() => this.handleServiceClick(service.id)}>
                                    {service.name}
                                </a>
                            </li>
                        ))}
                    </ul>
                </div>
            </div>
        );
    }
}
