package com.ecco.integration.homemaster;

import com.ecco.dto.ClientDefinition;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.composed.web.rest.json.PostJson;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

/**
 * Provides an implementation of the default REST API which delegates to Homemaster.
 */
@RestController
@RequestMapping("/homemaster")
public class HomemasterClientQueryAdapter {

    private Logger log = LoggerFactory.getLogger(getClass());

    private final HomemasterWebServiceClientConnector connector;

    @Autowired
    public HomemasterClientQueryAdapter(
            HomemasterWebServiceClientConnector connector) {
        this.connector = connector;
    }

    @PostJson(value = "/clients/", produces = APPLICATION_JSON_VALUE)
    public void updateClient(@RequestBody ClientDefinition newClient) {
        connector.updateClient(newClient);
    }

}
