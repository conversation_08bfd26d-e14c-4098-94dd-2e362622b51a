package com.ecco.infrastructure.entity;

import java.util.UUID;

import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.AccessType;
import org.hibernate.annotations.Type;

import javax.persistence.*;

/**
 * Abstract Entity that uses {@link AccessType#FIELD} access, and is for a {@link UUID} primary key
 */
@MappedSuperclass
public abstract class AbstractUUIDKeyedEntity extends AbstractUnidentifiedEntity<UUID> {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(generator="uuidTableGenerator")
    @GenericGenerator(name="uuidTableGenerator", strategy="com.ecco.infrastructure.entity.UseExistingOrGenerateUUIDGenerator")
    @Column(name="uuid", nullable=false, columnDefinition="CHAR(36)") // oracle doesn't like using unique=true
    @AccessType("property")
    @Type(type="org.hibernate.type.UUIDCharType")
    private UUID id = null;

    public AbstractUUIDKeyedEntity() {
    }

    protected AbstractUUIDKeyedEntity(UUID id) {
        this.id = id;
    }

    @Version
    @Column(name = "version")
    private Integer version;

    // version is for optimistic locking, so can't be set on an id or something
    // but would be nice if hibernate had an alternative to determine if the
    // object is saved at all
    // (we would use the id/isNew column)
    // but if we keep this as a value type and not an object then hibernate
    // doesn't think its a 'null or transient' object
    // see AbstractSaveEventListener ForeignKeys.Nullifier (around line 310)
    // and happily saves the association without specifying a value
    // unsaved-value is more legacy, so not implemented in annotations
    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    @Override
    public boolean isNewEntity() {
        return (getVersion() == null);
    }

    @Override
    public UUID getId() {
        return id;
    }

    @Override
    public void setId(UUID id) {
        this.id = id;
    }
}
