package com.ecco.webApi.controllers;

import com.ecco.evidence.repositories.ServiceRecipientRepository;
import com.ecco.infrastructure.hibernate.EntityUriMapper;
import com.ecco.service.LinearWorkflowService;
import com.ecco.serviceConfig.dom.ServiceTypeWorkflow;
import com.ecco.serviceConfig.service.ServiceTypeService;
import com.ecco.serviceConfig.viewModel.ServiceTypeViewModel;
import com.ecco.serviceConfig.viewModel.TaskDefinitionEntryViewModel;
import com.ecco.webApi.viewModels.WorkflowViewModel;
import com.ecco.workflow.WorkflowService;
import com.ecco.workflow.WorkflowTask;
import com.ecco.workflow.activiti.ActivitiWorkflowServiceImpl;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

import static java.util.stream.Collectors.toList;

@PreAuthorize("hasRole('ROLE_STAFF')")
@RestController
public class WorkflowController extends BaseWebApiController {

    private final ServiceRecipientRepository serviceRecipientRepository;

    private final ServiceTypeService serviceTypeService;

    private final ActivitiWorkflowServiceImpl activityWorkflowService;

    private final LinearWorkflowService linearWorkflowService;

    private final WorkflowTaskToViewModel workflowTaskToViewModel;

    public WorkflowController(ActivitiWorkflowServiceImpl activityWorkflowService,
                              LinearWorkflowService linearWorkflowService, ServiceRecipientRepository serviceRecipientRepository,
                              ServiceTypeService serviceTypeService,
                              final EntityUriMapper entityUriMapper) {
        this.activityWorkflowService = activityWorkflowService;
        this.linearWorkflowService = linearWorkflowService;
        this.serviceRecipientRepository = serviceRecipientRepository;
        this.serviceTypeService = serviceTypeService;
        this.workflowTaskToViewModel = new WorkflowTaskToViewModel(entityUriMapper);
    }


    @GetJson("/service-recipients/{serviceRecipientId}/workflow/")
    public WorkflowViewModel findOneWorkflowByServiceRecipientId(@PathVariable int serviceRecipientId) {
        // determined by BaseServiceRecipient
        long serviceTypeId = serviceRecipientRepository.findById(serviceRecipientId).orElseThrow().loadConfigServiceTypeId();
        WorkflowService workflowService = resolveWorkflowService(serviceRecipientId, serviceTypeId);

        // findOneDto cached version should be fine to assume exists
        ServiceTypeViewModel serviceType = serviceTypeService.findOneDto((int) serviceTypeId);
        String workflowProcessKey = serviceType.processKey;
        String tasksFilterKey = "tasksToShowRestricted"; // ignore tasksToShowClientView
        String tasksToShow = getTasksToShow(serviceType, tasksFilterKey);
        List<WorkflowTask> workflowTasks = workflowService.getFilteredWorkflowTasks(workflowProcessKey,
                String.valueOf(serviceRecipientId), tasksFilterKey, tasksToShow);

        WorkflowViewModel result = new WorkflowViewModel();
        result.serviceRecipientId = serviceRecipientId;
        result.tasks = workflowTasks.stream().map(workflowTaskToViewModel).collect(toList());
        return result;
    }

    private String getTasksToShow(ServiceTypeViewModel serviceType, String tasksFilterKey) {
        return serviceType.getTaskDefById(TaskDefinitionEntryViewModel.REFERRAL_VIEW).settings.get(tasksFilterKey);
    }

    /**
     * work out if we need Activiti or our linear workflow
     */
    private WorkflowService resolveWorkflowService(int serviceRecipientId, long serviceTypeId) {
        Optional<ServiceTypeWorkflow> workflow = serviceTypeService.findWorkflow(serviceTypeId);
        ServiceTypeViewModel serviceType = serviceTypeService.findOneDto(Long.valueOf(serviceTypeId).intValue());
        String workflowProcessKey = serviceType.processKey;

        boolean useActiviti = workflow.isPresent()
                && activityWorkflowService.activitiWorkflowEnabledFor(workflowProcessKey, String.valueOf(serviceRecipientId));
        return useActiviti ? activityWorkflowService : linearWorkflowService;
    }


}
