
-- =================
-- ===== SETUP =====
-- =================

-- CLIENTS TO DELETE

-- FOR ORACLE
    -- search group_concat(contactsId SEPARATOR ',') replace LISTAGG(contactsId, ',') within group (order by null)
    -- search group_concat(c.usersId) replace LISTAGG(c.usersId, ',') within group (order by null)
    -- search group_concat(u.username SEPARATOR '","') replace LISTAGG(u.username, ',') within group (order by null)
    -- search group_concat(id SEPARATOR ',') replace LISTAGG(id, ',') within group (order by null)
    -- search group_concat(eventId SEPARATOR ',') replace LISTAGG(eventId, ',') within group (order by null)
    -- search "<username>" replace '<username>'

-- FOR <clientIds>
-- CHECK empty - if there are several referrals - DO NOT DELETE THE CLIENT unless all referrals have been deleted
select id, clientId from referrals where clientId in (select clientId from referrals where clientId in (<clientId>) group by clientId having count(clientId) > 1);
    -- select group_concat(contactsId SEPARATOR ',') as contactsId from clientdetails where id in (<clientId>);
    -- select group_concat(c.usersId) as usersId from clientdetails cd inner join contacts c on cd.contactsId=c.id where cd.id in (<clientId>);
    -- select id as usersId from users where username in ("<username>");
    -- select id as contactsId from contacts where usersId in (<usersId>);
    -- select c.id, c.firstname, c.lastname, u.username as username from contacts c inner join users u on c.usersId=u.id where c.id in (<contactsId>);
    -- select group_concat(u.username SEPARATOR '","') as usernames from contacts c inner join users u on c.usersId=u.id where c.id in (<contactsId>);
    -- select group_concat(id SEPARATOR ',') as cosmouid from cosmo_users where username in ("<username>");
    -- select group_concat(id SEPARATOR ',') as itemids from cosmo_item where ownerid in (<cosmouid>);
    -- select group_concat(eventId SEPARATOR ',') as eventids from contacts_events where contactId in (<contactsId>);

-- FOR BULK (from remove_referral.sql)
-- if exists, skip, else put in the clientId wanted to delete
create table referral_archive_clients (clientId int, usersId int, username varchar(255), contactsId int, cosmouserid int);
-- add all at onces
insert into referral_archive_clients select cd.id, u.id, u.username, cd.contactsId, cu.id from clientdetails cd inner join contacts c on cd.contactsid = c.id inner join users u on c.usersid=u.id inner join cosmo_item ci on c.calendarId=ci.item_uid inner join cosmo_users cu on cu.id=ci.ownerid;
    -- select u.username, c.calendarId, ci.item_uid, ci.ownerid, cu.user_uid, cu.id, cu.username from users u inner join contacts c on u.id = c.usersid inner join cosmo_item ci on c.calendarId=ci.item_uid inner join cosmo_users cu on cu.id=ci.ownerid
    -- select * from referral_archive_clients;
-- add separately - usersId, username, contactsId from ecco
    # update referral_archive_clients a inner join clientdetails cd on a.clientId=cd.id inner join contacts c on cd.contactsId=c.id
    #  inner join users u on u.id=c.usersId
    #  set a.usersId=c.usersId, a.username=u.username, a.contactsId=c.id;
    -- select * from referral_archive_clients a inner join cosmo_users cu on a.username=cu.username;
    # update referral_archive_clients a inner join cosmo_users cu on a.username=cu.username
    #     set a.cosmouserid=cu.id;

-- CHECK empty - if there are several referrals - DO NOT DELETE THE CLIENT unless all referrals have been deleted
select distinct r.clientId from referrals r inner join referral_archive_clients a on r.clientId=a.clientId
where r.id not in (select referralid from referral_archive);
-- CHECK empty
select * from referral_archive_clients rac left join referrals r on rac.clientid=r.clientid where r.id is not null;

create table referral_archive_cosmoitem (itemid int);
insert into referral_archive_cosmoitem (itemid) select id from cosmo_item where ownerid in (select cosmouserid from referral_archive_clients);
-- if exists, skip
create table referral_archive_events (eventid int, cal_uid varchar(255));
insert into referral_archive_events (eventId, cal_uid) select id, cal_uid from events where contactId in (select contactsId from referral_archive_clients)
    and id not in (select eventId from referral_archive_events);


-- =================
-- ==== DELETE =====
-- =================

-- REFERRALS REMOVE
-- use <remove_referral.sql>
-- select id, clientId from referrals where clientId in (<clientId>);

-- CLIENTS REMOVE
-- delete client [if deleting a client, not just a user]
delete from clientdetails where id in (select clientId from referral_archive_clients);

-- delete events (also refer to <remove_referral.sql>)
delete from contacts_events where eventId in (select eventId from referral_archive_events);
delete from events where id in (select eventId from referral_archive_events);
delete from signature where individualId in (select contactsId from referral_archive_clients);
delete from contacts where id in (select contactsId from referral_archive_clients);

-- delete cosmo calendar entries
-- NB 'homeCollection' refers to 'collection' which holds all our entries
delete from cosmo_collection_item where collectionid in (select itemId from referral_archive_cosmoitem);
delete from cosmo_collection_item where itemid in (select itemId from referral_archive_cosmoitem);
delete from cosmo_tombstones where itemid in (select itemId from referral_archive_cosmoitem);
delete from cosmo_event_stamp where stampid in (select id from cosmo_stamp where itemid in (select itemId from referral_archive_cosmoitem));
delete from cosmo_stamp where itemid in (select itemId from referral_archive_cosmoitem);

-- delete cosmo users
update cosmo_item set modifiesitemid=null where modifiesitemid in (select itemId from referral_archive_cosmoitem);
delete from cosmo_item where ownerid in (select cosmouserid from referral_archive_clients);
delete from cosmo_users where id in (select cosmouserid from referral_archive_clients);

-- delete ecco user (not ACLs as only deleting clients)
delete from group_members where member_id in (select usersId from referral_archive_clients);
delete from group_members_AUD where member_id in (select usersId from referral_archive_clients);
-- delete from authorities where username in (select username from referral_archive_clients);
delete from users where id in (select usersId from referral_archive_clients);
-- TODO check this or just delete it
delete from users_AUD where id in (select usersId from referral_archive_clients);
delete from usr_commands where userIdSubject in (select usersId from referral_archive_clients);
delete from persistent_logins where username in (select username from referral_archive_clients);
-- delete from arc_audits where usersId in (select usersId from referral_archive_clients);
delete from passwordhistory where userId in (select usersId from referral_archive_clients);
delete from userdevices where users_id in (select usersId from referral_archive_clients);
delete from userdevices_AUD where users_id in (select usersId from referral_archive_clients);
delete from revision where username in (select username from referral_archive_clients);
delete from clientcommands where clientid in (select clientId from referral_archive_clients);

-- CHECK empty
?? select * from projectcomments;
