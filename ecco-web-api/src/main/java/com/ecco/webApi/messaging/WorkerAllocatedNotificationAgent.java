package com.ecco.webApi.messaging;

import com.ecco.config.service.SoftwareFeatureService;
import com.ecco.config.service.TemplateService;
import com.ecco.evidence.dom.TaskStatus;
import com.ecco.evidence.event.WorkerAllocatedEvent;
import com.ecco.evidence.repositories.ServiceRecipientRepository;
import com.ecco.evidence.repositories.TaskStatusRepository;
import com.ecco.infrastructure.bus.MessageBus;
import com.ecco.messaging.EmailService;
import com.ecco.security.repositories.UserRepository;
import com.ecco.serviceConfig.service.ServiceTypeService;
import com.ecco.webApi.taskFlow.ReferralTaskEditAllocateWorkerCommandViewModel;
import com.ecco.webApi.taskFlow.ReferralTaskEditStartOnServiceCommandViewModel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEvent;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.stream.Stream;


@RequiredArgsConstructor
@Slf4j
@Component
public class WorkerAllocatedNotificationAgent {

    @RequiredArgsConstructor
    @Getter
    static private class Params {
        private final int serviceRecipientId;
    }

    private final MessageBus<ApplicationEvent> messageBus;

    private final EmailService emailService;

    private final TemplateService templateService;

    private final TaskStatusRepository taskStatusRepository;

    private final UserRepository userRepository;

    private final SoftwareFeatureService softwareFeatureService;

    private final ServiceRecipientRepository serviceRecipientRepository;

    private final ServiceTypeService serviceTypeService;

    @Value("${enableEmailAllocated:false}")
    private boolean enableEmailAllocated;

    @PostConstruct
    protected void init() {
        messageBus.subscribe(WorkerAllocatedEvent.class, this::transferOpenTasks);
        messageBus.subscribe(WorkerAllocatedEvent.class, this::sendWorkerAllocatedEmailIfEnabled);
    }

    private void transferOpenTasks(WorkerAllocatedEvent event) {
        var vm = (ReferralTaskEditAllocateWorkerCommandViewModel) event.getSource();
        // only reallocate incomplete ('open') tasks
        if (vm.allocatedWorkerContactId != null) {
            Stream<TaskStatus> tasksToTransfer;
            var fromUser = vm.allocatedWorkerContactId.from != null ? userRepository.findByContactId(vm.allocatedWorkerContactId.from) : null;
            tasksToTransfer = taskStatusRepository.findAllByServiceRecipientIdAndAssignedUserAndCompletedNullAndDueDateIsNotNull(vm.serviceRecipientId, fromUser);
            var toUser = userRepository.findByContactId(vm.allocatedWorkerContactId.to);
            tasksToTransfer.forEach(t -> t.setAssignedUser(toUser));
        }
    }

    private void sendWorkerAllocatedEmailIfEnabled(WorkerAllocatedEvent event) {

        if (!softwareFeatureService.featureEnabled("ecco.email")) {
            // we don't log this elsewhere, it's the first thing that will be checked, and so avoids log entries
            //log.error("EMAIL NOT SENT - not enabled");
            return;
        }

        if (enableEmailAllocated) {
            sendWorkerAllocatedEmail(event);
        }
    }

    private void sendWorkerAllocatedEmail(WorkerAllocatedEvent event) {
        var eventSource = (ReferralTaskEditStartOnServiceCommandViewModel) event.getSource();
        var serviceRecipientId = eventSource.serviceRecipientId;

        // find out if the service config has email enabled
        Assert.isTrue(serviceRecipientId != null, "serviceRecipientId is null");
        var sr = serviceRecipientRepository.findById(serviceRecipientId).get();
        var type = serviceTypeService.findOneDto(sr.loadConfigServiceTypeId());
        var taskStart = type.getTaskDefByName(ReferralTaskEditStartOnServiceCommandViewModel.TASK_NAME);
        var taskAllocate = type.getTaskDefByName(ReferralTaskEditAllocateWorkerCommandViewModel.TASK_NAME);
        var task = taskStart != null ? taskStart : taskAllocate;
        var taskTrigger = "y".equalsIgnoreCase(task.settings.get("triggerEmail"));
        if (!taskTrigger) {
            // we don't log this elsewhere, it's the first thing that will be checked, and so avoids log entries
            return;
        }

        // assignee's email
        String email = event.getContact() == null ? null : event.getContact().getEmail();
        if (!StringUtils.hasText(email)) {
            log.error("EMAIL NOT SENT - no email address on contact");
            return;
        }

        try {
            Params params = new Params(serviceRecipientId);

            String body = templateService.getPopulatedTemplateWithSubstitutions("WorkerAssigned", params);

            if (body != null) {
                emailService.sendMessage(email, "New file assigned", body);
            } else {
                log.error("EMAIL NOT SENT - no body");
            }
        } catch (Exception e) {
            log.error("EMAIL FAILED", e);
        }
    }
}
