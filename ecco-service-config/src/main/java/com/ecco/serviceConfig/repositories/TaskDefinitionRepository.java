package com.ecco.serviceConfig.repositories;

import com.ecco.serviceConfig.dom.TaskDefinition;
import org.springframework.data.repository.CrudRepository;

import java.util.List;

public interface TaskDefinitionRepository extends CrudRepository<TaskDefinition, Long> {

    List<TaskDefinition> findAll();
    TaskDefinition findOneByNameIgnoreCase(String name);
    TaskDefinition findOneById(long id);

}
