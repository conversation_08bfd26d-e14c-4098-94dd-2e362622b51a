package com.ecco.security.dom;

import com.ecco.infrastructure.entity.AbstractLongKeyedEntity;

import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@SuppressWarnings("serial")
@Entity
@Table(name="ldapgroupmapping")
public class LdapGroupMapping extends AbstractLongKeyedEntity {

    private String ldapGroup;

    // since we added to this table for convenience acl-style entries, we want to confirm this field can be nullable
    @ManyToOne @JoinColumn(name = "localGroup", referencedColumnName = "group_name", nullable=true)
    private Group localGroup;

    private String localClass;

    private Long localId;


    protected LdapGroupMapping() {}

    /**
     * Each ldapgroupmapping can either refer to a local group name OR acl.
     * @see LdapUserDetailsContextMapper
     */
    public LdapGroupMapping(String ldapGroup, Group localGroup) {
        this.ldapGroup = ldapGroup;
        this.localGroup = localGroup;
    }

    /**
     * Each ldapgroupmapping can either refer to a local group name OR acl.
     * @see LdapUserDetailsContextMapper
     */
    public LdapGroupMapping(String ldapGroup, String localClass, long localId) {
        this.ldapGroup = ldapGroup;
        this.localClass = localClass;
        this.localId = localId;
    }

    public String getLdapGroup() {
        return ldapGroup;
    }

    public void setLdapGroup(String ldapGroup) {
        this.ldapGroup = ldapGroup;
    }

    public Group getLocalGroup() {
        return localGroup;
    }

    public void setLocalGroup(Group localGroup) {
        this.localGroup = localGroup;
    }

    public String getLocalClass() {
        return localClass;
    }
    public void setLocalClass(String localClass) {
        this.localClass = localClass;
    }
    public Long getLocalId() {
        return localId;
    }
    public void setLocalId(Long localId) {
        this.localId = localId;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = super.hashCode();
        result = prime * result + ((ldapGroup == null) ? 0 : ldapGroup.hashCode());
        result = prime * result + ((localClass == null) ? 0 : localClass.hashCode());
        result = prime * result + ((localGroup == null) ? 0 : localGroup.hashCode());
        result = prime * result + ((localId == null) ? 0 : localId.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
			return true;
		}
        if (!super.equals(obj)) {
			return false;
		}
        if (getClass() != obj.getClass()) {
			return false;
		}
        LdapGroupMapping other = (LdapGroupMapping) obj;
        if (ldapGroup == null) {
            if (other.ldapGroup != null) {
				return false;
			}
        } else if (!ldapGroup.equals(other.ldapGroup)) {
			return false;
		}
        if (localClass == null) {
            if (other.localClass != null) {
				return false;
			}
        } else if (!localClass.equals(other.localClass)) {
			return false;
		}
        if (localGroup == null) {
            if (other.localGroup != null) {
				return false;
			}
        } else if (!localGroup.equals(other.localGroup)) {
			return false;
		}
        if (localId == null) {
            if (other.localId != null) {
				return false;
			}
        } else if (!localId.equals(other.localId)) {
			return false;
		}
        return true;
    }

}
