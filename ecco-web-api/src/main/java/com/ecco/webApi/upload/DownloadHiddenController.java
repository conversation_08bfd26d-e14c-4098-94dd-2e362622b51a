package com.ecco.webApi.upload;

import com.ecco.dom.upload.UploadedFile;
import com.ecco.exceptions.BaseApplicationExceptionRuntime;
import com.ecco.service.upload.UploadService;
import com.ecco.web.upload.UploadConfig;
import com.ecco.web.upload.UploadConfigGenerator;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.annotation.Secured;
import org.springframework.stereotype.Controller;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.context.support.WebApplicationObjectSupport;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

// a generated document has a controller to generate the data, and then a view which points to the type of file
// however, in this case, the view is the bytes - so the controller needs to just stream the bytes to the response

// need WebApplicationObjectSupport for the getServletContext() mime types
@Controller
@RequiredArgsConstructor
public class DownloadHiddenController extends WebApplicationObjectSupport {

    // as copied from WebContentGenerator
    private static final String HEADER_PRAGMA = "Pragma";
    private static final String HEADER_CACHE_CONTROL = "Cache-Control";

    private final UploadService uploadService;
    private final UploadConfigGenerator uploadConfigGenerator;

    // eg http://localhost:8888/ecco-war/api/secure/export/download?id=<svcrec_attachments.id>&source=service-recipient
    @GetMapping("/export/download")
    @Secured("ROLE_EXPORT")
    public void getFileExport(@RequestParam(value="id") Integer fileId,
                        HttpServletRequest request, HttpServletResponse response) {
        getFile(fileId, request, response);
    }

    // NB should probably use StreamingResponseBody - see https://stackoverflow.com/questions/35680932/download-a-file-from-spring-boot-rest-service
    @GetMapping("/secure/downloadHidden.html")
    public void getFile(@RequestParam(value="id") Integer fileId,
            HttpServletRequest request, HttpServletResponse response) {

        UploadConfig<?> config = uploadConfigGenerator.constructUploadConfig(request, null);
        UploadedFile uploadedFile = uploadService.getFile(fileId, config);
        final byte[] bytes = uploadedFile.getUploadedBytes().getBytes();
        final String filename = uploadedFile.getFilename();

        // spring-way from their AbstractView
        // specifically, it sets these for downloads - to prevent ssl ie problem
        // response.setHeader("Pragma", "private");
        // response.setHeader("Cache-Control", "private, must-revalidate");
        // and writes the contentLength before the stream (at least in pdf, the excel has "Should we set the content length here?"!)
        // but we know the size, so we don't need the buffer

        // monitor: can monitor the result by turning on the RequestDumperValve in the server.xml

        // response.setBufferSize?
        // cacheSeconds on views?
        // "Expires" header?

        // code snippets threaded from http://forum.springsource.org/showthread.php?t=10181
        // ensure: no-cache isn't used (even though standard), since IE forces it - and it needs to cache to then do something with it once its streamed!
        //        this is also from the text in PrintoutLabelView
        // ensure: compression option in the server.xml is off
        // ensure: file ending is set for IE - else downloading doesn't really happen
        // test: ff3, no-store can be used, but we allow caching in private space (through pragma)
        // test: if IE always shows known mime-types, if so, try this http://forum.springframework.org/showthread.php?t=14128
        // test: open file in IE failes, if so its because content-disposition causes 2 requests, second without cookies (and hence session), see http://forum.springframework.org/showthread.php?t=14128
        response.setHeader("Content-Disposition", "attachment; filename=\"" + filename +"\"");
        // test: ie7 ssl download
        //         ie7 ssl problem, requires excel with these headers - see http://expisoft.blogspot.com/2007/03/ie7-does-not-excel.html
        //        response.setHeader("Pragma", "private");
        //        response.setHeader("Cache-control", "private, must-revalidate");
        // test: different file types, could use application/octet-stream but seems there are more problems with ie! - http://stackoverflow.com/questions/2232103/how-do-i-get-csv-file-to-download-on-ie-works-on-firefox

        // from PrintOutLabelView max-age is required for a download to work (without no-cache)
        // and private is the best place to cache, since cache seems to be necessary
        // from AbstractView - private, must-revalidate to prevent 'IE bug'
        response.setHeader(HEADER_CACHE_CONTROL, "max-age=0, private, must-revalidate");
        response.setHeader(HEADER_PRAGMA, "private");

        // mimetypes are got from conf/web.xml
        // though can be altered, eg http://don-software-dev.blogspot.com/2009/08/tomcat-mime-type-settings.html
        // <mime-mapping>
        //       <extension>txt</extension>
        //       <mime-type>text/plain</mime-type>
        // </mime-mapping>
        String mimetype = getServletContext().getMimeType(filename);
        response.setContentType(mimetype);
        response.setContentLength(bytes.length);
        try (ServletOutputStream  out = response.getOutputStream()) {
            FileCopyUtils.copy(bytes, out);
            out.flush();
        } catch (IOException e) {
            throw new BaseApplicationExceptionRuntime("downloadError", e);
        }
    }

}
