package com.ecco.webApi.incidents;

import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.commands.CreateServiceRecipientCommand;
import com.ecco.dom.incidents.Incident;
import com.ecco.repositories.incidents.IncidentRepository;
import com.ecco.serviceConfig.repositories.ServiceCategorisationRepository;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.controllers.CreateServiceRecipientParams;
import com.ecco.webApi.evidence.ServiceRecipientCommandHandler;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jetbrains.annotations.NotNull;
import org.springframework.security.core.Authentication;
import org.springframework.util.Assert;

import javax.annotation.Nonnull;
import java.io.Serializable;

import static com.ecco.infrastructure.rest.hateoas.ApiLinkTo.linkToApi;
import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.methodOn;

public class CreateIncidentCommandHandler extends ServiceRecipientCommandHandler<CreateIncidentCommandViewModel,
        CreateServiceRecipientCommand, CreateServiceRecipientParams> {

    @Nonnull
    private final IncidentRepository incidentRepository;
    private IncidentFromViewModel incidentFromViewModel;

    public CreateIncidentCommandHandler(ObjectMapper objectMapper,
                                        @Nonnull ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                                        IncidentRepository incidentRepository,
                                        IncidentFromViewModel incidentFromViewModel) {
        super(objectMapper, serviceRecipientCommandRepository, CreateIncidentCommandViewModel.class);
        this.incidentRepository = incidentRepository;
        this.incidentFromViewModel = incidentFromViewModel;
    }

    @Override
    protected CreateServiceRecipientCommand createCommand(Serializable targetId, CreateServiceRecipientParams params, String requestBody,
                                                          CreateIncidentCommandViewModel viewModel, long userId) {
        Assert.state(params.getPrefix().equals(viewModel.getPrefix()), "prefix in body must match URI");

        return new CreateServiceRecipientCommand(
                viewModel.uuid,
                viewModel.timestamp,
                userId,
                requestBody,
                (Integer) targetId);
    }

    @Override
    protected CommandResult handleInternal(@NotNull Authentication auth, CreateServiceRecipientParams params, @NotNull CreateIncidentCommandViewModel viewModel) {
        int id = CreateIncidentCommandHandler.createIncident(viewModel.getIncidentViewModel(), this.incidentFromViewModel,
                this.incidentRepository);
        var incident = this.incidentRepository.getById(id);

        // return the srId for the createCommand
        return CommandResult.ofLink(linkToApi(methodOn(IncidentController.class).getIncident(id)).withSelfRel())
                .withTargetId(incident.getServiceRecipient().getId());
    }

    public static int createIncident(IncidentViewModel viewModel, IncidentFromViewModel fromViewModel,
                                     IncidentRepository incidentRepository) {
        final Incident incident = fromViewModel.apply(viewModel);
        incidentRepository.save(incident);
        return incident.getId();
    }

}
