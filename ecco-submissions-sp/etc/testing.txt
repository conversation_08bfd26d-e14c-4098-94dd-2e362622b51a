soapUI - version 4.5.1
	help at http://www.javahotchocolate.com/tutorials/soapui.html
	new project with following wsdl
	https://supportingpeople.st-andrews.ac.uk/webservices/spwebservices.asmx?wsdl
	then find the methods - double click the serviceSoap12 -> serviceStatus -> Request 1 and play it...response OK

	then double click the serviceSoap12 -> uploadXml -> Request 1
	paste in below...
	"if you pass in the parameter test instead of upload and use the providers API key you will not upload any logs to our system."
		the API key is required (we use the bridge's api) - from faq: "Unfortunately no XML documents will be accepted from software companies using the testing API keys they are provided with. They must ask their clients to provide the API key for integration with their software."

	used their example data from website, with online tool no probs - but got submission probs, however, needed wrapping in CDATA
	<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope" xmlns:web="https://supportingpeople.st-andrews.ac.uk/webservices/">
   	<soap:Header/>
	   <soap:Body>
	      <web:uploadXML>
	         <!--Optional:-->
	         <web:APIKEY>5E45E654D71070940037703F0EC4B7362D40BB28</web:APIKEY>
	         <!--Optional:-->
	         <web:XMLData>
				<![CDATA[<ClientRecords xsi:schemaLocation="https://supportingpeople.st-andrews.ac.uk CRF_SCHEMA.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><!--Providers Details --><!--start of providers details--><SubmissionDetails><Forename>aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa</Forename><Surname>aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa</Surname><Telephone>aaaaaaaaaaaa</Telephone><Email>_</Email><DateCompleted>1967-08-13</DateCompleted></SubmissionDetails><!--end of providers details--><!-- first client record form --><ClientRecord><ProviderServiceDetails><!--must be five digits--><ClientRecordID>10000</ClientRecordID><!--must be eight digits--><NationalID>10000000</NationalID><OrgName>aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa</OrgName><ServiceName>aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa</ServiceName><ServiceID>aaaaaaaaaaaaaaaaaaaaaaaaaaaaaa</ServiceID><AdminAuthority>E10000032</AdminAuthority><ServiceType>1</ServiceType><Partnership>1</Partnership><PartnershipHealth>true</PartnershipHealth><PartnershipSocial>true</PartnershipSocial><PartnershipHousing>true</PartnershipHousing><PartnershipDrug>true</PartnershipDrug><PartnershipPolice>true</PartnershipPolice><PartnershipYOT>true</PartnershipYOT><PartnershipEducation>true</PartnershipEducation><PartnershipBenefits>true</PartnershipBenefits><PartnershipDebt>true</PartnershipDebt><PartnershipEmployment>true</PartnershipEmployment><PartnershipOther>true</PartnershipOther></ProviderServiceDetails><ClientDetails><ClientBudget>1</ClientBudget><ClientBudgetUsed>1</ClientBudgetUsed><RentDepositScheme>1</RentDepositScheme><SupportStart>2012-04-01</SupportStart><ClientCode>aaaaaaaaaaaa</ClientCode><InterviewRefused>true</InterviewRefused><HouseholdMembers><Client><Age>0</Age><Sex>_</Sex><Relationship>_</Relationship><EconomicStatus>-1</EconomicStatus></Client><Person2><Age>0</Age><Sex>_</Sex><Relationship>_</Relationship><EconomicStatus>-1</EconomicStatus></Person2><Person3><Age>0</Age><Sex>_</Sex><Relationship>_</Relationship><EconomicStatus>-1</EconomicStatus></Person3><Person4><Age>0</Age><Sex>_</Sex><Relationship>_</Relationship><EconomicStatus>-1</EconomicStatus></Person4><Person5><Age>0</Age><Sex>_</Sex><Relationship>_</Relationship><EconomicStatus>-1</EconomicStatus></Person5><Person6><Age>0</Age><Sex>_</Sex><Relationship>_</Relationship><EconomicStatus>-1</EconomicStatus></Person6></HouseholdMembers><NINumber>_</NINumber><NIUnknown>true</NIUnknown><NIRefused>true</NIRefused><NoNINo>true</NoNINo><Disabled>1</Disabled><DisabilityMobility>true</DisabilityMobility><DisabilityVisual>true</DisabilityVisual><DisabilityHearing>true</DisabilityHearing><DisabilityChronic>true</DisabilityChronic><DisabilityMental>true</DisabilityMental><DisabilityLearning>true</DisabilityLearning><DisabilityAutistic>true</DisabilityAutistic><DisabilityOther>true</DisabilityOther><DisabilityRefused>true</DisabilityRefused><EthnicOrigin>0</EthnicOrigin><UserDefinedEthnic>aaaaaaaaaa</UserDefinedEthnic><Religion>0</Religion><sexualOrientation>1</sexualOrientation><transgender>0</transgender><exArmedForces>2</exArmedForces><PrimaryClientGroup>0</PrimaryClientGroup><SecondaryClientGroup1>0</SecondaryClientGroup1><SecondaryClientGroup2>0</SecondaryClientGroup2><SecondaryClientGroup3>0</SecondaryClientGroup3><userClientGroup>Test entry</userClientGroup><CareManagement>1</CareManagement><secondMentalHealth>1</secondMentalHealth><ProbationYouthOffending>1</ProbationYouthOffending><DrugInterventions>1</DrugInterventions><Homeless>1</Homeless><CPA>1</CPA><MAPPA>1</MAPPA><MARAC>1</MARAC><HasASBO>1</HasASBO><ReferralSource>1</ReferralSource><ReferralType>1</ReferralType><FloatStartAccom>0</FloatStartAccom><PreviousTenure>0</PreviousTenure><CurrentTenure>true</CurrentTenure><PreviousLACode>E07000223</PreviousLACode><PreviousPostcode1>_</PreviousPostcode1><PreviousPostcode2>_</PreviousPostcode2><TempAccomodation>true</TempAccomodation><ServiceAAYears>0</ServiceAAYears><ServiceAAMonths>0</ServiceAAMonths><ServiceAADays>0</ServiceAADays><Under6MonthsLACode>E07000223</Under6MonthsLACode><Under6MonthsLAYears>22</Under6MonthsLAYears><Under6MonthsLAMonths>0</Under6MonthsLAMonths><Under6MonthsLADays>0</Under6MonthsLADays><HostDurationUnknown>true</HostDurationUnknown><PreviousLAYears>0</PreviousLAYears><PreviousLAMonths>0</PreviousLAMonths><PreviousLADays>0</PreviousLADays><NonHostDurationUnknown>true</NonHostDurationUnknown></ClientDetails></ClientRecord><!-- end of first client record--><!-- second client record form --><ClientRecord><ProviderServiceDetails><ClientRecordID>10000</ClientRecordID><NationalID>10000000</NationalID><OrgName>aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa</OrgName><ServiceName>aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa</ServiceName><ServiceID>aaaaaaaaaaaaaaaaaaaaaaaaaaaaaa</ServiceID><AdminAuthority>E10000007</AdminAuthority><ServiceType>1</ServiceType><Partnership>1</Partnership><PartnershipHealth>true</PartnershipHealth><PartnershipSocial>true</PartnershipSocial><PartnershipHousing>true</PartnershipHousing><PartnershipDrug>true</PartnershipDrug><PartnershipPolice>true</PartnershipPolice><PartnershipYOT>true</PartnershipYOT><PartnershipEducation>true</PartnershipEducation><PartnershipBenefits>true</PartnershipBenefits><PartnershipDebt>true</PartnershipDebt><PartnershipEmployment>true</PartnershipEmployment><PartnershipOther>true</PartnershipOther></ProviderServiceDetails><ClientDetails><ClientBudget>1</ClientBudget><ClientBudgetUsed>1</ClientBudgetUsed><RentDepositScheme>1</RentDepositScheme><SupportStart>2012-04-01</SupportStart><ClientCode>aaaaaaaaaaaa</ClientCode><InterviewRefused>true</InterviewRefused><HouseholdMembers><Client><Age>0</Age><Sex>_</Sex><Relationship>_</Relationship><EconomicStatus>-1</EconomicStatus></Client><Person2><Age>0</Age><Sex>_</Sex><Relationship>_</Relationship><EconomicStatus>-1</EconomicStatus></Person2><Person3><Age>0</Age><Sex>_</Sex><Relationship>_</Relationship><EconomicStatus>-1</EconomicStatus></Person3><Person4><Age>0</Age><Sex>_</Sex><Relationship>_</Relationship><EconomicStatus>-1</EconomicStatus></Person4><Person5><Age>0</Age><Sex>_</Sex><Relationship>_</Relationship><EconomicStatus>-1</EconomicStatus></Person5><Person6><Age>0</Age><Sex>_</Sex><Relationship>_</Relationship><EconomicStatus>-1</EconomicStatus></Person6></HouseholdMembers><NINumber>_</NINumber><NIUnknown>true</NIUnknown><NIRefused>true</NIRefused><NoNINo>true</NoNINo><Disabled>1</Disabled><DisabilityMobility>true</DisabilityMobility><DisabilityVisual>true</DisabilityVisual><DisabilityHearing>true</DisabilityHearing><DisabilityChronic>true</DisabilityChronic><DisabilityMental>true</DisabilityMental><DisabilityLearning>true</DisabilityLearning><DisabilityAutistic>true</DisabilityAutistic><DisabilityOther>true</DisabilityOther><DisabilityRefused>true</DisabilityRefused><EthnicOrigin>0</EthnicOrigin><UserDefinedEthnic>aaaaaaaaaa</UserDefinedEthnic><Religion>0</Religion><sexualOrientation>0</sexualOrientation><transgender>0</transgender><exArmedForces>0</exArmedForces><PrimaryClientGroup>0</PrimaryClientGroup><SecondaryClientGroup1>0</SecondaryClientGroup1><SecondaryClientGroup2>0</SecondaryClientGroup2><SecondaryClientGroup3>0</SecondaryClientGroup3><userClientGroup>Example text</userClientGroup><CareManagement>1</CareManagement><secondMentalHealth>1</secondMentalHealth><ProbationYouthOffending>1</ProbationYouthOffending><DrugInterventions>1</DrugInterventions><Homeless>1</Homeless><CPA>1</CPA><MAPPA>1</MAPPA><MARAC>1</MARAC><HasASBO>1</HasASBO><ReferralSource>1</ReferralSource><ReferralType>1</ReferralType><FloatStartAccom>0</FloatStartAccom><PreviousTenure>0</PreviousTenure><CurrentTenure>true</CurrentTenure><PreviousLACode>E07000032</PreviousLACode><PreviousPostcode1>_</PreviousPostcode1><PreviousPostcode2>_</PreviousPostcode2><TempAccomodation>true</TempAccomodation><ServiceAAYears>0</ServiceAAYears><ServiceAAMonths>0</ServiceAAMonths><ServiceAADays>0</ServiceAADays><Under6MonthsLACode>E07000032</Under6MonthsLACode><Under6MonthsLAYears>0</Under6MonthsLAYears><Under6MonthsLAMonths>0</Under6MonthsLAMonths><Under6MonthsLADays>0</Under6MonthsLADays><HostDurationUnknown>true</HostDurationUnknown><PreviousLAYears>0</PreviousLAYears><PreviousLAMonths>0</PreviousLAMonths><PreviousLADays>0</PreviousLADays><NonHostDurationUnknown>true</NonHostDurationUnknown></ClientDetails></ClientRecord><!-- end of second client record --><!-- XML end --></ClientRecords>]]>
	         </web:XMLData>
	         <!--Optional:-->
	         <web:option>test</web:option>
	      </web:uploadXML>
	   </soap:Body>
	</soap:Envelope>



soap testing:
	http://www.soapclient.com/soaptest.html
	org.apache.ws.tcpmon
	soapui installation
soap error handling (for a server?): http://blog.espenberntsen.net/2010/02/28/spring-ws-client/
search "spring-ws client exceptions" to know eg what IO can be caught etc

localhost tcp monitor for windows [can use firefox etc for http]
soap current example:
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:web="https://www.spclientrecord.org.uk/webservices/">
   <soapenv:Header/>
   <soapenv:Body>
      <web:uploadXML>
         <web:APIKEY>40</web:APIKEY>
         <web:XMLData><ClientRecords xsi:schemaLocation="https://www.spclientrecord.org.uk CRF_SCHEMA.xsd" xmlns="https://www.spclientrecord.org.uk" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><SubmissionDetails><Forename>forename1</Forename><Surname>surname1</Surname><Telephone>tel1</Telephone><Email><EMAIL></Email><DateCompleted>2010-11-02</DateCompleted></SubmissionDetails></ClientRecords></web:XMLData>
         <web:option>test</web:option>
      </web:uploadXML>
   </soapenv:Body>
</soapenv:Envelope>
fails on at java.io.Reader.<init>(Unknown Source)
