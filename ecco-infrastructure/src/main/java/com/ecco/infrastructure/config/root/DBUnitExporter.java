package com.ecco.infrastructure.config.root;

import javax.sql.DataSource;

import org.dbunit.database.DatabaseConnection;
import org.dbunit.database.IDatabaseConnection;
import org.dbunit.dataset.FilteredDataSet;
import org.dbunit.dataset.IDataSet;
import org.dbunit.dataset.filter.ExcludeTableFilter;
import org.dbunit.dataset.xml.FlatXmlDataSet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.jdbc.datasource.DataSourceUtils;

import java.io.FileOutputStream;
import java.sql.Connection;

/**
 * Export dbunit files when containing {@link ApplicationContext} emits {@link ContextRefreshedEvent}.
 * Ignores DATABASECHANGELOG for safety, but will still need the schema's to match as its imorting data assumed to be the same structure
 * This
 */
public class DBUnitExporter implements ApplicationListener<ContextRefreshedEvent> {

    private final ApplicationContext context;
    private final DataSource dataSource;

    @Autowired
    public DBUnitExporter(ApplicationContext context, DataSource dataSource) {
        this.context = context;
        this.dataSource = dataSource;
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (event.getApplicationContext().equals(context)) {
            try {
                doExport();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
    }

    // see also streaming options at http://dbunit.sourceforge.net/faq.html
    private void doExport() throws Exception {

        Connection c = DataSourceUtils.getConnection(this.dataSource);

        IDatabaseConnection con = new DatabaseConnection(c);

        // avoid errors by not exporting the schema at all
        ExcludeTableFilter exFilter = new ExcludeTableFilter(new String [] {"DATABASECHANGELOG"});
        IDataSet exDataset = new FilteredDataSet(exFilter, con.createDataSet());

        // we change from using dtd to using fk disabling - (see DBUnitImporter)
        /*
        String[] schemaTableNames = exDataset.getTableNames(); // new String[] {"clientdetails"};

        // we use the sequencer to get the right order as best we can, edit it and do away with this class
        // [ACT_RE_DEPLOYMENT, ACT_GE_BYTEARRAY, ACT_GE_PROPERTY, ACT_HI_ACTINST, ACT_HI_ATTACHMENT, ACT_HI_COMMENT, ACT_HI_DETAIL, ACT_HI_PROCINST, ACT_HI_TASKINST, ACT_HI_VARINST, ACT_ID_GROUP, ACT_ID_INFO, ACT_ID_USER, ACT_ID_MEMBERSHIP, ACT_RE_MODEL, ACT_RE_PROCDEF, ACT_RU_EXECUTION, ACT_RU_EVENT_SUBSCR, ACT_RU_TASK, ACT_RU_IDENTITYLINK, ACT_RU_JOB, ACT_RU_VARIABLE, DATABASECHANGELOG, DATABASECHANGELOGLOCK, accommodationCategories, regions, projects, accommodations, acl_class, acl_sid, acl_object_identity, acl_entry, evidenceguidance, questiongroups, outcomes, risks, actionfakes, actions, actionlinks, outcomebenefits, actions_outcomebenefits, addresstypes, agencycategories, exitreasons, familytypes, fundingsources, offences, pendingstatuses, referralcomments, referrersources, religions, servicetypes, services, signpostreasons, uploadbytes, cfg_externalsystem, economicstatuses, resourcetypes, users, contacts, ethnicorigins, languages, clientdetails, contracts, referrals, appointmentagreements, appointmenttypes, financebands, appointmentschedules, audits, authorities, blacklistedips, cfg_feature, cfg_menu, cfg_module, cfg_menuitem, cfg_menu_cfg_menuitem, clickstreams, commandqueue, commenttypes, workers, events, contacts_events, contracts_actions, cosmo_content_data, cosmo_users, cosmo_item, cosmo_attribute, cosmo_collection_item, cosmo_dictionary_values, cosmo_event_log, cosmo_stamp, cosmo_event_stamp, cosmo_multistring_values, cosmo_pwrecovery, cosmo_server_properties, cosmo_subscription, cosmo_tickets, cosmo_ticket_privilege, cosmo_tombstones, cosmo_user_preferences, countries, departments, failedlogins, flags, groups, group_authorities, group_members, revision, group_members_AUD, groupsupportactivitytypes, groupsupportvenues, groupsupportactivities, reviews, signature, supportplanwork, groupsupportactivities_clients, groupsupportactivities_workers, gsat_linkedactions, hibernate_sequences, hr, hr_outcomes, jobs, leavereasons, workers_jobs, hrfromtos, hrsettings, individual_individualTypes, ldapgroupmapping, likelihoods, localauthorities, locales, messages, nextmeetinglocations, outcomethreats_actionsupports, questions, outcomethreats_questions, passwordhistory, persistent_logins, projectcomments, questionanswerchoices, questionanswerfrees, questiongroups_questions, questions_questionanswerfrees, questions_questionanswrchoices, referral_to_gsat, referralactivitytypes, referralactivities, referralactivityworker, referralaspects, svcrec_attachments, referralcommands, referralprojects, referrals_contacts, reports, reportattachments, services_projects, servicetypes_flagthreats, servicetypes_outcomeservices, servicetypes_outcomesupports, servicetypes_outcomethreats, servicetypes_questiongroups, servicetypes_referralaspects, servicetypes_workflow, setting, severity, st_referralaspectsettings, submissions, supporthrwork, supporthractions, supporthrcomments, supporthroutcomes, supporthrwork_actions, supportplanactions, supportplananswers, supportthreatwork, supportplancomments, supportplanoutcomes, supportplanrisks, supportplanwork_actions, supportthreatactions, supportthreatcomments, supportthreatflags, supportthreatoutcomes, supportthreatwork_actions, supportworkattachments, uploadfile, templates, userdevices, userdevices_AUD, users_AUD, work, workerattachments]
        String tablesOrdered = "ACT_RE_DEPLOYMENT, ACT_GE_BYTEARRAY, ACT_GE_PROPERTY, ACT_HI_ACTINST, ACT_HI_ATTACHMENT, ACT_HI_COMMENT, ACT_HI_DETAIL, ACT_HI_PROCINST, ACT_HI_TASKINST, ACT_HI_VARINST, ACT_ID_GROUP, ACT_ID_INFO, ACT_ID_USER, ACT_ID_MEMBERSHIP, ACT_RE_MODEL, ACT_RE_PROCDEF, ACT_RU_EXECUTION, ACT_RU_EVENT_SUBSCR, ACT_RU_TASK, ACT_RU_IDENTITYLINK, ACT_RU_JOB, ACT_RU_VARIABLE, DATABASECHANGELOG, DATABASECHANGELOGLOCK, accommodationCategories, regions, projects, accommodations, acl_class, acl_sid, acl_object_identity, acl_entry, evidenceguidance, questiongroups, outcomes, risks, actionfakes, actions, actionlinks, outcomebenefits, actions_outcomebenefits, addresstypes, agencycategories, exitreasons, familytypes, fundingsources, offences, pendingstatuses, referrersources, religions, servicetypes, services, signpostreasons, uploadbytes, cfg_externalsystem, economicstatuses, resourcetypes, users, contacts, ethnicorigins, languages, clientdetails, contracts, referrals, referralcomments, appointmentagreements, appointmenttypes, financebands, appointmentschedules, audits, authorities, blacklistedips, cfg_feature, cfg_menu, cfg_module, cfg_menuitem, cfg_menu_cfg_menuitem, clickstreams, commandqueue, commenttypes, workers, events, contacts_events, contracts_actions, cosmo_content_data, cosmo_users, cosmo_item, cosmo_attribute, cosmo_collection_item, cosmo_dictionary_values, cosmo_event_log, cosmo_stamp, cosmo_event_stamp, cosmo_multistring_values, cosmo_pwrecovery, cosmo_server_properties, cosmo_subscription, cosmo_tickets, cosmo_ticket_privilege, cosmo_tombstones, cosmo_user_preferences, countries, departments, failedlogins, flags, groups, group_authorities, group_members, revision, group_members_AUD, groupsupportactivitytypes, groupsupportvenues, groupsupportactivities, reviews, signature, supportplanwork, groupsupportactivities_clients, groupsupportactivities_workers, gsat_linkedactions, hibernate_sequences, hr, hr_outcomes, jobs, leavereasons, workers_jobs, hrfromtos, hrsettings, individual_individualTypes, ldapgroupmapping, likelihoods, localauthorities, locales, messages, nextmeetinglocations, outcomethreats_actionsupports, questions, outcomethreats_questions, passwordhistory, persistent_logins, projectcomments, questionanswerchoices, questionanswerfrees, questiongroups_questions, questions_questionanswerfrees, questions_questionanswrchoices, referral_to_gsat, referralactivitytypes, referralactivities, referralactivityworker, referralaspects, svcrec_attachments, svcrec_commands, referralprojects, referrals_contacts, reports, reportattachments, services_projects, servicetypes_flagthreats, servicetypes_outcomeservices, servicetypes_outcomesupports, servicetypes_outcomethreats, servicetypes_questiongroups, servicetypes_referralaspects, servicetypes_workflow, setting, severity, st_referralaspectsettings, submissions, supporthrwork, supporthractions, supporthrcomments, supporthroutcomes, supporthrwork_actions, supportplanactions, supportplananswers, supportthreatwork, supportplancomments, supportplanoutcomes, supportplanrisks, supportplanwork_actions, supportthreatactions, supportthreatcomments, supportthreatflags, supportthreatoutcomes, supportthreatwork_actions, supportworkattachments, uploadfile, templates, userdevices, userdevices_AUD, users_AUD, work, workerattachments";
        String[] tablesArr = StringUtils.split(tablesOrdered, ", ");
        // don't need the sequencer now we have used it to get the ordering so far
        //ITableFilter seqFilter = new DBUnitExporterSequencer(con, schemaTableNames);
        ITableFilter seqFilter = new SequenceTableFilter(tablesArr);
        IDataSet seqExDataset = new FilteredDataSet(seqFilter, exDataset);

        // write out
        FlatDtdDataSet.write(seqExDataset, new FileOutputStream("dbunit-schema.dtd"));
        FlatXmlWriter datasetWriter = new FlatXmlWriter(new FileOutputStream("dbunit-export.xml"));
        datasetWriter.setDocType("dbunit-schema.dtd");
        datasetWriter.write(seqExDataset);
        */

        // write out (no need for dtd with foreign key disabling)
        FlatXmlDataSet.write(exDataset, new FileOutputStream("dbunit-export.xml"));
    }

}
