package com.ecco.serviceConfig.dom;

import javax.persistence.*;
import java.io.Serializable;

@Embeddable
public class ServiceType_OutcomeSupport_MultiId implements Serializable {

    private static final long serialVersionUID = 1L;

    @OneToOne(fetch=FetchType.LAZY, optional=false)
    @JoinColumn(name="serviceTypeId")
    ServiceType serviceType;

    @OneToOne(fetch= FetchType.LAZY, optional=false)
    @JoinColumn(name="outcomeId")
    OutcomeSupport outcome;

    protected ServiceType_OutcomeSupport_MultiId() {
    }

    public ServiceType_OutcomeSupport_MultiId(ServiceType serviceType, OutcomeSupport outcome) {
        this.serviceType = serviceType;
        this.outcome = outcome;
    }

    public ServiceType getServiceType() {
        return serviceType;
    }

    public void setServiceType(ServiceType serviceType) {
        this.serviceType = serviceType;
    }

    public OutcomeSupport getOutcome() {
        return outcome;
    }

    public void setOutcome(OutcomeSupport outcome) {
        this.outcome = outcome;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ServiceType_OutcomeSupport_MultiId that = (ServiceType_OutcomeSupport_MultiId) o;

        if (serviceType != null ? !serviceType.equals(that.serviceType) : that.serviceType != null) return false;
        return outcome != null ? outcome.equals(that.outcome) : that.outcome == null;

    }

    @Override
    public int hashCode() {
        int result = serviceType != null ? serviceType.hashCode() : 0;
        result = 31 * result + (outcome != null ? outcome.hashCode() : 0);
        return result;
    }
}
