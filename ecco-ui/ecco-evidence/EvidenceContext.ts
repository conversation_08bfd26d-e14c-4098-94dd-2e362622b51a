import {SessionData, ConfigResolver, ActivityType, ServiceRecipientWithEntities} from "ecco-dto";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {EvidenceDef} from "./domain";
import {SmartStepStatusTransitions} from "./smartsteps/SmartStepStatusTransitions";

/**
 * Context for various params such as style etc which we might want to access up and down the tree
 * @param evidencePageType an indication of how to display the state of the smart step
 */
export class EvidenceContext {
    constructor(
        public serviceRecipientId: number,
        public serviceRecipientWithEntities: ServiceRecipientWithEntities | null,
        public serviceRecipientActivityInterest: ActivityType[],
        public getWorkUuid: () => Uuid,
        public evidenceDef: EvidenceDef,
        public statusTransitions: SmartStepStatusTransitions,
        public features: SessionData,
        public configResolver: ConfigResolver,
        public asForwardPlan: boolean = false,
        public asForwardRiskPlan: boolean = false,
        public asForwardCarePlan: boolean = false
    ) {}
}
