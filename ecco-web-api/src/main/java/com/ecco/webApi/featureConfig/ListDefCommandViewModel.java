package com.ecco.webApi.featureConfig;

import javax.annotation.Nonnull;

import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.dto.ChangeViewModel;
import org.jetbrains.annotations.NotNull;
import org.springframework.web.util.UriComponentsBuilder;

public class ListDefCommandViewModel extends BaseCommandViewModel {

    @Nonnull
    public String operation;

    /** Existing id of the list def entry (not for 'add' commands) */
    public Integer id;

    public String nameNew;

    public ChangeViewModel<String> businessKey;

    public ChangeViewModel<String> entryName;

    public ChangeViewModel<String> listName;

    public ChangeViewModel<Integer> listNameId;

    public ChangeViewModel<Integer> order;

    public ChangeViewModel<Integer> parentId;

    public ChangeViewModel<Boolean> disabled;

    public ChangeViewModel<Boolean> defaultForList;

    public ChangeViewModel<String> metaValue;

    public ChangeViewModel<String> metaDisplayName;

    @Deprecated
    public ListDefCommandViewModel() {
        super();
    }

    public ListDefCommandViewModel(@NotNull String operation) {
        super(UriComponentsBuilder
                        .fromUriString("feature-config/listDef/")
                        .toString());
        this.operation = operation;
    }

    public ListDefCommandViewModel(@NotNull String operation, String commandUri) {
        super(commandUri);
        this.operation = operation;
    }

    @Override
    public String toString() {
        return "ListDefCommandViewModel [operation=" + operation + ", id=" + id + ", nameNew=" + nameNew
                + ", listName=" + listName + ", parentId=" + parentId
                + ", entryName=" + entryName
                + ", businessKey=" + businessKey
                + ", order=" + order
                + ", disabled=" + disabled + ", defaultForList=" + defaultForList + ", metaValue=" + metaValue
                + ", metaDisplayName=" + metaDisplayName + ", uuid=" + uuid + ", commandUri=" + commandUri
                + ", timestamp=" + timestamp + "]";
    }

}
