
/** Spring messages that are exposed to JavaScript.
 *
 * These messages are exposed by com.ecco.offline.SpringMessagesOfflineResourceProvider. */
export interface Messages {
    "invalidNewPassword_ONE_DIGIT_ONE_UPPER": string;
    "invalidNewPassword_ONE_DIGIT_ONE_UPPER_ONE_SPECIAL_MIN_8": string;
    "invalidNewPassword_SIMPLE": string;
    "invalidNewPassword_PREVIOUS": string;

    "status.forAssessment": string;
    "status.pending": string;
    "status.started": string;
    "status.toStart": string;
    "status.signposted": string;
    "status.incomplete": string;
    "status.exited": string;
    "status.hidden": string;
    "referralBreadcrumb.region": string;
    "referralBreadcrumb.project": string;
    "referralBreadcrumb.staffDetail": string;
    "referralBreadcrumb.clientWithContact": string;
    "referralBreadcrumb.relationshipToPrimary": string;
    "referralBreadcrumb.dataProtection": string;
    "referralBreadcrumb.consent": string;
    "referralBreadcrumb.source": string;
    "referralBreadcrumb.referralDetails": string;
    "referralBreadcrumb.referralView": string;

    "referralView.allocateWorker": string;
    "referralView.staffDetail": string;
    "referralView.staffLocation": string;
    "referralView.clientWithContact": string;
    "referralView.referralAccepted": string;
    "referralView.decideFinal": string;
    "referralView.sourceWithIndividual": string;
    "referralView.scheduleReviews": string;
    "referralView.assessmentDate": string;
    "referralView.start": string;
    "referralView.startAccommodation": string;
    "referralView.referralDetails": string;
    "referralView.funding": string;
    "referralView.pendingStatus": string;
    "referralView.waitingListCriteria": string;
    "referralView.close": string;
    "referralView.assessmentDetails": string;
    "referralView.project": string;
    "referralView.dataProtection": string;
    "referralView.consent": string;
    "referralView.newMultipleReferral": string;
    "referralView.emergencyDetails": string;
    "referralView.iaptInitialAssessment": string;
    "referralView.iaptAttendance": string;
    "referralView.iaptCurrentView": string;
    "referralView.iaptSessions": string;
    "referralView.iaptGoals": string;
    "referralView.iaptImpact": string;
    "referralView.iaptFeedback": string;
    "referralView.nextMeeting": string;
    "referralView.agreementOfAppointments": string;
    "referralView.scheduleMeetings": string;
    "referralView.allocateToServices": string;
    "referralView.initial-sp_data": string;
    "referralView.exit-sp_data": string;
    "referralView.engagementComments": string;
    "referralView.feedbackQuestionnaire": string;
    "referralView.groupActivities": string;
    "referralView.generalQuestionnaire": string;
    "referralView.hactQuestionnaire": string;
    "referralView.managerNotes": string;
    "referralView.needsAssessment": string;
    "referralView.needsReduction": string;
    "referralView.needsReductionSP": string;
    "referralView.needsAssessmentReduction": string;
    "referralView.needsAssessmentReductionReview": string;
    "referralView.rotaVisit": string;
    "referralView.supportStaffNotes": string;
    "referralView.threatAssessment": string;
    "referralView.threatReduction": string;
    "referralView.threatAssessmentReduction": string;
    "referralView.customForm1": string;
    "referralView.customForm2": string;
    "referralView.customForm3": string;
    "referralView.customForm4": string;
    "referralView.customForm5": string;
    "referralView.customForm6": string;
    "referralView.customForm7": string;
    "referralView.customForm8": string;
    "referralView.customForm9": string;
    "referralView.customForm10": string;
    "referralView.customForm11": string;
    "referralView.customForm12": string;
    "referralView.customForm13": string;
    "referralView.customForm14": string;
    "referralView.customForm15": string;
    "referralView.customForm16": string;
    "referralView.customForm17": string;
    "referralView.customFormMgr": string;
    "workerJobView.jobDetails": string;

    "referralView.tabs.overview": string;
    "referralView.tabs.risk": string;
    "referralView.tabs.support": string;
    "referralView.tabs.supportHistory": string;
    "referralView.tabs.visitHistory": string;
    "referralView.tabs.checklistHistory": string;
    "referralView.tabs.riskHistory": string;
    "referralView.tabs.formHistory": string;
    "referralView.tabs.forwardPlan": string;
    "referralView.tabs.forwardRiskPlan": string;
    "referralView.tabs.appointments": string;
    "referralView.tabs.relationshipStar": string;
    "referralView.tabs.services": string;
    "reportEntity.Referral": string;
    "reportEntity.ReferralFull": string;
    "reportEntity.ReferralSummary": string;
    "reportEntity.SupportWork": string;
    "reportEntity.RiskWork": string;
    "reportEntity.SupportRiskWork": string;
    "reportEntity.RiskFlags": string;
    "reportEntity.SupportFlags": string;
    "reportEntity.RiskRatings": string;
    "reportEntity.ActivityAttendance": string;
    "reportEntity.Questionnaire": string;
    "reportEntity.QuestionnaireSnapshot": string;
    "reportEntity.QuestionnaireMultiSnapshot": string;
    "reportEntity.QuestionnaireSingleSnapshot": string;
    "reportEntity.RotaAgreement": string;
    "reportEntity.RotaDemand": string;
    "reportEntity.RotaSchedule": string;
    "reportEntity.SmartStepSnapshot": string;
    "reportEntity.ServiceRecipient": string;
    "reportEntity.ServiceRecipientCommand": string;
    "reportEntity.GroupedWorkAnalysis": string;
    "reportEntity.ReferralsByMonth": string;
    "reportEntity.ReferralsByService": string;
    "reportEntity.ReferralsBySource": string;
    "reportEntity.ReferralsByEthnicity": string;
    "reportEntity.ReferralsBySexualOrientation": string;
    "reportEntity.ReferralsByDisability": string;
    "reportEntity.AnswersByQuestion": string;
    "reportEntity.TasksByMonth": string;
    "reportEntity.Client": string;
    "reportEntity.AssociatedContact": string;
    "reportEntity.Agency": string;
    "reportEntity.TaskStatus": string;
    "reportEntity.User": string;
    "reportEntity.Review": string;
    "reportEntity.ServiceType": string;
    "reportEntity.AddressHistory": string;
    "reportEntity.OccupyHistory": string;
    "reportEntity.FinanceCharge": string;
    "reportEntity.FinanceReceipt": string;
    "reportEntity.SupportWorkSnapshot": string;
    "reportEntity.RiskWorkSnapshot": string;
    "reportEntity.CustomFormSnapshot": string;
    "reportEntity.Building": string;
    "reportEntity.Repair": string;

    "form.agreements.signature": string;
    "form.funding.reviewDate": string;
    "form.funding.paymentReference": string;
    "form.assessmentDate.firstOfferedInterviewDate": string;
    "form.emergencyDetails.description": string;
    "form.emergencyDetails.communicationNeeds": string;
    "form.emergencyDetails.communicationKeyword": string;
    "form.emergencyDetails.emergencyDetails": string;
    "form.emergencyDetails.medicationDetails": string;
    "form.emergencyDetails.risksAndConcerns": string;
    "form.emergencyDetails.doctorsDetails": string;
    "form.emergencyDetails.dentistDetails": string;
    "form.evidenceComment.location": string;

    "terminology.smartstep": string;
    "project": string;
    "projects": string;
    "signpost": string;
    "signpostBack": string;
    "signpostComment": string;

    "goalName.label": string; // default overridden by {taskName}.goalPlan.label
    "needsAssessment.goalName.label": string;
    "needsReduction.goalName.label": string;
    "needsAssessmentReduction.goalName.label": string;
    "needsAssessmentReductionReview.goalName.label": string;
    "threatAssessment.goalName.label": string;
    "threatAssessmentReduction.goalName.label": string;
    "threatReduction.goalName.label": string;
    "goalPlan.label": string; // default overridden by {taskName}.goalPlan.label
    "needsAssessment.goalPlan.label": string;
    "needsReduction.goalPlan.label": string;
    "needsAssessmentReduction.goalPlan.label": string;
    "needsAssessmentReductionReview.goalPlan.label": string;
    "threatAssessment.goalPlan.label": string;
    "threatAssessmentReduction.goalPlan.label": string;
    "threatReduction.goalPlan.label": string;

    "threat.triggerControl.hazard.label": string;
    "threat.triggerControl.intervention.label": string;
    "threat.goal.new.label": string;
    "threat.subGoal.new.label": string;
    "support.goal.new.label": string;
    "support.subGoal.new.label": string;
}

