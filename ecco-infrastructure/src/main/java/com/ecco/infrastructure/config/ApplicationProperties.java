package com.ecco.infrastructure.config;

import java.util.Map;

/**
 * Ecco application properties that are exposed to the view layer.
 * <p/>
 * Application properties are exposed to JavaScript by serializing them as
 * JSON. Therefore, this interface, and implementations of this interface,
 * must be serializable as JSON using Jackson Data Binding.
 * <p/>
 * Application properties are also exposed through Spring MVC as part of
 * the model reference data.
 */
public interface ApplicationProperties {

    /**
     * The root path of the Ecco web application (the servlet context root).
     * <p/>
     * The path ends with a slash ('/'), so it is possible to use it as a base
     * path against which to perform relative path resolution as defined by
     * RFC 3986 section 5.2.3.  Note that this is different from the path
     * returned by {@link javax.servlet.ServletContext#getContextPath()},
     * which does <b>not</b> end with a slash.
     */
    String getApplicationRootPath();

    /**
     * Cache-busted resource root path.
     * <p/>
     * The path ends with a slash ('/'), so it is possible to use it as a base
     * path against which to perform relative path resolution as defined by
     * RFC 3986 section 5.2.3.
     */
    String getResourceRootPath();

    /** Whether username/password is allowed, or just login providers */
    boolean isLoginProvidersOnly();

    /**
     * List of providers on the environment, eg 'azure'. See application-properties.d.ts.
     */
    Map<String, Boolean> getLoginProviders();

    /**
     * Whether to load the user calendar events through the loginProvider.
     */
    boolean isProviderCalendarEnabled();

    /**
     * The main address of the site.
     * <p/>
     * For when we don't have access to a URL (because something is @Scheduled), but
     * we need to know how to generate email links.
     * e.g. -Decco.websiteUrl=demo.eccosolutions.co.uk
     * Similar to target.host.url for tests.
     */
    String getEccoWebsiteUrl();

    /**
     * The git branch that the build was created from
     */
    String getGitBranch();

    /**
     * The full 'gitish' of the commit of the build
     */
    String getGitCommitId();

    /**
     * Useful description of the build relative to a recent tag (see man for
     * git-describe for details)
     */
    String getGitCommitIdDescribe();

    /**
     * Time of the commit that this build relates to
     */
    String getGitCommitTime();

    /**
     * The java.version system variable giving us the version of the JRE we are running e.g. 1.7.0_55
     */
    String getJavaVersion();

    /**
     * The year from the git commit time for use in copyright footer.
     */
    String getCopyrightYear();
}
