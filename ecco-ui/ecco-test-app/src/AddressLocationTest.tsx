import * as React from "react";
import {AddressLocation} from "ecco-components";

export default function AddressLocationTest() {

    return (
        <>
            {/* 301231 https://demo.eccosolutions.co.uk/uat/nav/referrals/100115 */}
            <AddressLocation
                serviceRecipientId={301231}
                showBuildings={true}
                showValidFrom={true}
                displayAddress={null}
                addressLocationId={null}
                handleAddressValidChange={() => {}}
            />
            <hr />
            {/*<p>validFrom: {JSON.stringify(appointmentFields)}</p>*/}
        </>
    );
}