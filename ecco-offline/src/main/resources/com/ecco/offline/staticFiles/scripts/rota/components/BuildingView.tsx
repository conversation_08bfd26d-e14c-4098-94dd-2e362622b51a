import {applicationRootPath} from "application-properties";
import {
    EccoTheme,
    EccoV3Modal,
    handleLazy,
    LoadingSpinner,
    ServiceAgreementsView,
    CareRunList,
    TabsBuilder,
    useAdminModeMenu,
    useBuilding,
    useAppBarOptions, useServicesContext
} from "ecco-components";
import * as React from "react";
import {FC, useState} from "react";
import {lazyControlWrapper} from "../../components/ControlWrapper";
import {AuditHistory} from "../../service-recipients/components/AuditHistory";
import {Box, Button} from '@eccosolutions/ecco-mui';

type Props = {
    buildingId: number
};

/**
 * From the rota, pop up a view
 */
export const BuildingViewPopup: FC<Props> = props => {

    const [show, setShow] = useState(false);

    const {building} = useBuilding(props.buildingId);

    return <span>
        <Button style={{minWidth: 32, minHeight: 40}} onClick={() => setShow(true)}>
            <i className="fa fa-external-link"/>
        </Button>
        <EccoTheme prefix="bldgView">
            <EccoV3Modal
                title={building?.name ?? "loading..."}
                show={show}
                onCancel={() => setShow(false)}
                action="close"
            >
                <BuildingView buildingId={props.buildingId} />
            </EccoV3Modal>
        </EccoTheme>
    </span>;
};

export const BuildingView: FC<Props> = props => {
    const {building, loading} = useBuilding(props.buildingId);
    const {sessionData} = useServicesContext();
    useAdminModeMenu("config mode");
    // within a rota building popup icon (top menu) this causes react errors
    //useAppBarOptions(`${building?.name}`, [building?.buildingId]);

    if (loading || !building) return <LoadingSpinner/>;

    const StaffInBuilding = lazyControlWrapper(
        // @ts-ignore - because typescript
        () => import("../../hr/WorkersListControl"),
        props.buildingId
    );

    const ResidentsInBuilding = lazyControlWrapper(
        // @ts-ignore
        () => import("../../referral/ReferralsListControl"), false, props.buildingId, true
    );

    // Separate component because TabsBuilder has hooks and we've got a conditional return above
    const Content = () => <>
        <Box p={2}>
            {new TabsBuilder()
                .addTab("overview",
                    <div className={"entityForm"}>Building:&nbsp;
                        <a href={`${applicationRootPath}nav/service-recipient/${building.serviceRecipientId}`} target={"_blank"}>
                            {building.name}
                        </a>
                    </div>,
                    undefined, "fa-user")
                .addTab("staff", handleLazy(<StaffInBuilding/>), undefined, "fa-user")
                .addTab("residents", handleLazy(<ResidentsInBuilding/>), undefined, "fa-user")
                .addTab("care runs", handleLazy(<CareRunList buildingId={building.buildingId}/>))
                .addTab("patterns",
                    <ServiceAgreementsView serviceRecipientId={building.serviceRecipientId}/>,
                    undefined, "fa-calendar")
                .addTab( "audit history",
                    <AuditHistory serviceRecipientId={building.serviceRecipientId} sessionData={sessionData}/>,
                    undefined, "fa-history")
                .build()
            }
        </Box>
    </>;
    return <Content/>;
};
export default BuildingView;