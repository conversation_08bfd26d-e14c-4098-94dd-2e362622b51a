import {EvidenceGroup, FormEvidence} from "../evidence-dto";
import {FormDefinition} from "../form-definition-dto";
import {EccoDateTime} from "@eccosolutions/ecco-common";

export interface FormEvidenceRepository {
    /** @deprecated should migrate to GlobalConfig.findFormDefinition() */
    findFormDefinition(uuid: string): Promise<FormDefinition | null>;

    /**
     * Retrieve the latest evidence snapshot, unless snapshotWorkUuid is provided.
     * /service-recipients/{serviceRecipientId}/evidence/form/{evidenceGroupKey}/snapshots/latest/
     * @param findLastSigned From the latest (or snapshot) snapshot, go back to return the last signed entry.
     */
    findLatestFormEvidenceSnapshotByServiceRecipientId(
        serviceRecipientId: number,
        evidenceGroup: EvidenceGroup
    ): Promise<FormEvidence<any> | null>;

    findOneFormEvidenceWorkByServiceRecipientId(
        serviceRecipientId: number,
        evidenceGroup: EvidenceGroup | null,
        snapshotWorkUuid: string,
        findLastSignedSnapshot?: boolean
    ): Promise<FormEvidence<any> | null>;

    findAllFormEvidenceByServiceRecipientId(
        serviceRecipientId: number,
        evidenceGroup?: EvidenceGroup,
        pageNumber?: number,
        attachmentsOnly?: boolean
    ): Promise<FormEvidence<any>[]>;

    findAllBetweenFormEvidenceByServiceRecipientId(
        serviceRecipientId: number,
        evidenceGroup: EvidenceGroup,
        fromDateTime: EccoDateTime,
        toDateTime: EccoDateTime
    ): Promise<FormEvidence<any>[]>;
}
