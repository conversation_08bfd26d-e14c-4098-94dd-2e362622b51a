// needs application-properties which is online only currently
// url: localhost:8080/ecco-war/r/noCache/scripts/tests/hact/hact-test.html
// offline: file:///<path to>/ecco/ecco-offline/src/main/resources/com/ecco/offline/staticFiles/scripts/tests/hact/hact-test.html
import qunit = require("qunit");
import Lazy = require("lazy");
import Sequence = LazyJS.Sequence; // Note: LazyJS not Lazy, due to how module is wired.
import testData = require("../testData");
import {HactSocialValue, hactSocialValueAnalyser,
        HactManagementData, hactManagementAnalyser} from "../../reports/analysis/hactAnalysis";
import {NotificationSummaryData} from "../../evidence/hact/hactNotificationHandlerData"
import {EccoDate, EccoDateTime} from "@eccosolutions/ecco-common";
import HactNotificationHandler = require("../../evidence/hact/HactNotificationHandler");
import {HactClientCompositeDataFull} from "../../evidence/hact/hactClientCompositeData";
import {
    clientHactQnWork1, clientHactQnWork2b, clientHactReportCriteria, clientHactSupportWork1,
    testHactSessionData
} from "../testData";
import {SupportWork} from "ecco-dto/evidence-dto";
import {QuestionnaireWorkDto} from "ecco-dto";


// NB haven't tested pre-survey notifications just because this would be seen instantly

/**
 * Common test function used by post notifications 1 and 2 tests to check the hact questions
 * @param now the date to test things by
 * @param hactAnswers is the provided survey data, which is used to calculate against the support data in this method
 * @param preDue
 * @param postDue
 * @param preAll
 * @param postAll
 */
let postNotificationsTest = function (now: EccoDateTime, hactAnswers: QuestionnaireWorkDto[],
                                      preDue: number[] = [], postDue: number[] = [100,200],
                                      preAll: number[] = [], postAll: number[] = [100,200]) {

    // 2016-04-05 three smart steps trigger
    // actionId 105: trigger outcome EMP1401, EMP1402, EMP1403 but with the same question for each: 'What is your employment status'"
    // actionId 200: trigger outcome HEA1401 with question: 'Have you recently been losing confidence in yourself'
    // actionId 300: trigger outcome FIN1406 with question: 'How well would you say you yourself are managing financially these days?'
    let work: SupportWork[] = clientHactSupportWork1;
    let clientId = 1; // dummy value - used in HactAnalysisFromHactAnswerHistoryAccumulator simply as the 'key'
    let hactClientCompositeData = new HactClientCompositeDataFull(testHactSessionData, clientId, hactAnswers, work, []);

    // handler for due questions
    let handlerDue = new HactNotificationHandler(hactClientCompositeData, now, false);
    // test values consumed by the controllers
    let totalHactQuestionsDue = handlerDue.getQuestionsOutstanding();
    let preOutstandingDue = handlerDue.getPreSurveyQuestionsOutstanding();
    let postOutstandingDue = handlerDue.getPostSurveyQuestionsOutstanding();

    // we expect 2 post (and 0 pre) notifications because we already have an answer
    // we don't expect actionId 300 because its expired whereas the other actionId 105 and actionId 200 have pre answers
    qunit.equal(totalHactQuestionsDue.length, preDue.length + postDue.length, "due total surveys outstanding matches");
    qunit.equal(preOutstandingDue.length, preDue.length, "due pre surveys outstanding matches");
    qunit.equal(postOutstandingDue.length, postDue.length, "due post surveys outstanding matches");
    if (postOutstandingDue.length) {
        qunit.equal(postOutstandingDue[0].id, postDue[0], "due post survey outstanding questionId matches");
        qunit.equal(postOutstandingDue[1].id, postDue[1], "due post survey outstanding questionId matches");
    }

    // handler for all questions - which should behave almost identically to due questions
    let handlerAll = new HactNotificationHandler(hactClientCompositeData, now, true);
    // test values consumed by the controllers
    let totalHactQuestionsAll = handlerAll.getQuestionsOutstanding();
    let preOutstandingAll = handlerAll.getPreSurveyQuestionsOutstanding();
    let postOutstandingAll = handlerAll.getPostSurveyQuestionsOutstanding();

    // we expect 2 post (and 0 pre) notifications because we already have an answer
    // we don't expect actionId 300 because its expired whereas the other actionId 105 and actionId 200 have pre answers
    qunit.equal(totalHactQuestionsAll.length, preAll.length + postAll.length, "all total surveys outstanding matches");
    qunit.equal(preOutstandingAll.length, preAll.length, "all pre surveys outstanding matches");
    qunit.equal(postOutstandingAll.length, postAll.length, "all post surveys outstanding matches");
    if (postOutstandingAll.length) {
        qunit.equal(postOutstandingAll[0].id, postAll[0], "all post survey outstanding questionId matches");
        qunit.equal(postOutstandingAll[1].id, postAll[1], "all post survey outstanding questionId matches");
    }
};

// test a date when we've answered the pre survey in time (3 days after triggering), so we get a post 1 notification later on
qunit.test("'hact': post 1 notifications due (done pre)", () => {
    // the report to date is 2017-05-19 which is after the expiry of the unanswered preSurvey date
    // (daysPreToPost1 is 3 months which means the unanswered pre survey expires on 2016-07-26)
    // NB we did increase daysTriggerToPreLimit (see 15a7f9bf) which meant the preSurvey hadn't expired
    // so we decided to up the report to date to 2017-05-19 from 2016-08-26 to resolve it
    let now: EccoDateTime = EccoDate.parseIso8601(clientHactReportCriteria.to).toDateTimeMidnight();

    // 2016-04-08 preSurvey
    // questionId: 100, // What is your employment status? -> Unemployed [EMP1401 / 2 / 3]
    // questionId: 200, // Have you recently been losing confidence in yourself -> Not at all [HEA1401]
    // questionId: 300 is purposefully NOT answered but is triggered (see postNotificationsTest)
    let preAnswer = clientHactQnWork1;
    let answers: QuestionnaireWorkDto[] = [preAnswer];

    postNotificationsTest(now, answers);
});

// test a date when we miss the post 1 window of 240 days (8 months), so we get an opportunity in post 2
qunit.test("'hact': post 2 notifications due (missed post 1, done pre)", () => {
    // after the postAnswer1 by 14 months since pre
    let now: EccoDateTime = EccoDate.parseIso8601("2017-03-01").toDateTimeMidnight();

    // 2016-04-08 with 'status: unemployed' and 'confidence: not at all'
    let preAnswer = clientHactQnWork1;
    let answers: QuestionnaireWorkDto[] = [preAnswer];

    postNotificationsTest(now, answers);
});

// test a date when we answer pre and post 1, so we get an opportunity to answer again in post 2
qunit.test("'hact': post 2 notifications due (done post 1, done pre)", () => {
    // after the postAnswer1 by daysPost1ToPost2 of 6 months, but within the 14 months since pre
    let now: EccoDateTime = EccoDate.parseIso8601("2017-03-01").toDateTimeMidnight();

    // 2016-04-08 with 'status: unemployed' and 'confidence: not at all'
    let preAnswer = clientHactQnWork1;
    // 2016-08-08 with 'status: unemployed' and 'confidence: not at all'
    // with 'status: self-employed' and 'confidence: living comfortably'
    // 3 months after the preAnswer and within the daysPreToPost1Limit of 8 months
    let postAnswer1 = clientHactQnWork2b;
    let answers: QuestionnaireWorkDto[] = [postAnswer1, preAnswer]; // ORDER MATTERS
    postNotificationsTest(now, answers);
});

// 'close off' test that we can answer a survey value that isn't due yet (eg pre survey -> not due post 1)
qunit.test("'hact': post 1 notifications not due, but are outstanding", () => {
    // a date just before the start of the post 1 period, which is the pre answer + 3 months (daysPreToPost1Trigger of 90)
    let now: EccoDateTime = EccoDate.parseIso8601("2016-06-25").toDateTimeMidnight();

    let preAnswer = clientHactQnWork1;
    let answers: QuestionnaireWorkDto[] = [preAnswer]; // ORDER MATTERS

    postNotificationsTest(now, answers, [100], [], [100], [100,200]);
});

// TODO check HactNotificationHandler.ts getDaysExpire for Infinity

// TODO test notification shows across client
// TODO test mgt report has more than one qn per client (DEV-1869)
// TODO confirm historical triggers answered now can still have surveys done
// TODO test for multiple smart steps (although appears to do the right thing - merges them together)
// TODO confirm no pre-surveys for year 2
// TODO confirm re-trigger pre-surveys for year 1
// TODO confirm what happens when post 1 and post 2 have 'more valuable' answers - eg tenure shift
// TODO confirm re-trigger pre-survey from multiple/other smart steps - see hactNotificationHandlerData.build - actions can be multiple smart steps... just collects days diff
// TODO confirm no pre-surveys for year 2 - takes holistic data and works out expiry etc based on year 1 only
// TODO useful test for historical triggers can still have surveys done

qunit.test("'hact': valuable report", () => {
    // NB we could probably test the analyser by running a report def through
    let socialValueData: Sequence<HactSocialValue> = hactSocialValueAnalyser(null, Lazy(testData.clientHactReferralAgg1)).getData();
    let firstRow = socialValueData.first();
    qunit.equal(firstRow.clientCode, "C001", "cid matches");
    qunit.equal(firstRow.questionDefId, 100, "questionDefId matches");
    qunit.equal(firstRow.answerChoiceDefId1, 151, "answerChoiceDefId1 matches");
    qunit.equal(firstRow.answerChoiceDefId2, 154, "answerChoiceDefId2 matches");
    qunit.equal(firstRow.valuableChange, true, "valuableChange true");
    qunit.equal(firstRow.valuableHactOutcomeDefCode, "EMP1402", "valuableHactCode matches");
    // 25 at 2016-08-08T00:00 the valuable answer workDate2. dob 1991-08-08
    qunit.equal(firstRow.clientAgeAtValuableAnswer, 25, "clientAgeAtValuableAnswer matches");
    qunit.equal(firstRow.valuableHactValue, 11887, "valuableHactValue (outside London, < 49) matches");
    // work1 is the latest - April
    qunit.equal(firstRow.workDate1.compare(EccoDateTime.parseIso8601("2016-04-08T00:00")), 0, "workDate1 matches");
    // work2 is the next latest - August
    qunit.equal(firstRow.workDate2.compare(EccoDateTime.parseIso8601("2016-08-08T00:00")), 0, "workDate2 matches");

    // ensure that the new test data does NOT cause an extra question valuable change
    // because the extra data starts off valuable - hence is not a valuable change
    qunit.strictEqual(socialValueData.size(), 1, "report rows matches");

    // agg3 is testing question 300 from an initial valuable answer to a different valuable answer
    socialValueData = hactSocialValueAnalyser(null, Lazy(testData.clientHactReferralAgg3)).getData();
    qunit.strictEqual(socialValueData.size(), 1, "report rows matches");
    qunit.strictEqual(socialValueData.first().valuableChange, false, "valuable changes don't match");

    // agg4 is testing question 400 to a valuable answer done earlier than the notification (just 2 days after!) - due to 'close off'
    socialValueData = hactSocialValueAnalyser(null, Lazy(testData.clientHactReferralAgg5)).getData();
    qunit.strictEqual(socialValueData.size(), 1, "report rows matches from 'close off' early valuable answer");
});


qunit.test("'hact': management report", () => {
    let managementData: Sequence<HactManagementData> = hactManagementAnalyser(null, Lazy(testData.clientHactReferralAgg2)).getData();
    let reportNow = EccoDate.parseIso8601(testData.clientHactReferralAgg2[0].reportCriteria.to).toDateTimeMidnight();

    // scenario 1 rows: answered, expired, due

    // firstRow = client 1, question 1
    let firstRow = managementData.first();
    qunit.equal(firstRow.clientCode, "C001", "cid matches");
    qunit.equal(firstRow.questionDefId, 100, "questionDefId matches");
    qunit.equal(firstRow.surveyName, "pre survey", "status name matches");
    qunit.equal(firstRow.preSurveySupportWorkDate.compare(EccoDateTime.parseIso8601("2016-04-05T00:00")), 0, "pre survey support date matches");
    qunit.equal(firstRow.surveyStatus, "answered", "pre survey status matches");
    qunit.equal(firstRow.surveyAnswerWorkDate.compare(EccoDateTime.parseIso8601("2016-04-08T00:00")), 0, "pre survey answer date matches");
    qunit.equal(firstRow.surveyDaysUntilExpire, NotificationSummaryData.daysTriggerToPreLimit-(8-5), "pre survey days until expire matches");

    // postSurvey1 is now expired with the new report date: the pre survey was 2016-04-08 and the report to date is 2017-05-19
    let secondRow = managementData.toArray()[1];
    qunit.equal(secondRow.surveyName, "post survey 1", "status name matches");
    qunit.equal(secondRow.surveyStatus, "expired", "post survey 1 status matches");
    let preSurveyAnswerDate = EccoDateTime.parseIso8601("2016-04-08T00:00");
    qunit.equal(secondRow.surveyAnswerWorkDate, null, "post survey 1 answer date matches");
    let daysPreToPost1Expire = NotificationSummaryData.daysPreToPost1Limit - (reportNow.subtractDateTime(preSurveyAnswerDate, true).getDays());
    qunit.equal(secondRow.surveyDaysUntilExpire, daysPreToPost1Expire, "post survey 1 days until expire matches");
    let daysPreToLastExpire = NotificationSummaryData.daysPreToEndHardLimit - (reportNow.subtractDateTime(preSurveyAnswerDate, true).getDays());

    let thirdRow = managementData.toArray()[2];
    qunit.equal(thirdRow.surveyName, "post survey 2", "status name matches");
    qunit.equal(thirdRow.surveyStatus, "due", "post survey 2 status matches");
    qunit.equal(thirdRow.surveyAnswerWorkDate, null, "post survey 2 answer date matches");
    qunit.equal(thirdRow.surveyDaysUntilExpire, daysPreToLastExpire, "post survey 2 days until expire matches");

    // TODO scenario 2: answered, answered, due
    // secondRow = client 1, question 2

    // scenario 3: not answered pre-survey
    let managementData3: Sequence<HactManagementData> = hactManagementAnalyser(null, Lazy(testData.clientHactReferralAgg4)).getData();
    let reportNow3 = EccoDate.parseIso8601(testData.clientHactReferralAgg4[0].reportCriteria.to).toDateTimeMidnight();

    let firstRow3 = managementData3.first();
    qunit.equal(firstRow3.clientCode, "C001", "no pre-survey: cid matches");
    qunit.equal(firstRow3.surveyName, "pre survey", "status name matches");
    // 'expired' because 'now' is taken as the reportDate (reportCriteria.to), being '2016-08-26' against the work date of '2016-04-05' (20 weeks = 143)
    qunit.equal(firstRow3.surveyStatus, "expired", "no pre-survey: pre survey status matches");
    qunit.equal(firstRow3.preSurveySupportWorkDate.compare(EccoDateTime.parseIso8601("2016-04-05T00:00")), 0, "no pre-survey: pre survey support date matches");
    qunit.equal(firstRow3.surveyAnswerWorkDate, null, "no pre-survey: pre survey null answer date matches");
    let supportWorkDate3 = EccoDateTime.parseIso8601("2016-04-05T00:00");
    let daysTriggerToPreExpire3 = reportNow3.subtractDateTime(supportWorkDate3, true).getDays();
    qunit.equal(firstRow3.surveyDaysUntilExpire, -1*(daysTriggerToPreExpire3 - NotificationSummaryData.daysTriggerToPreLimit), "no pre-survey: pre survey days expired matches");

    let secondRow3 = managementData3.toArray()[1];
    qunit.equal(secondRow3.surveyName, "post survey 1", "status name matches");
    qunit.equal(secondRow3.surveyStatus, "not allowed", "no pre-survey: post survey 1 status matches");
    qunit.equal(secondRow3.surveyAnswerWorkDate, null, "no pre-survey: post survey 1 null answer date matches");
    let mimicPreSurveyAnswerDate = secondRow3.preSurveySupportWorkDate.addDays(NotificationSummaryData.daysTriggerToPreLimit);
    let daysPreToPost1Expire3 = NotificationSummaryData.daysPreToPost1Limit - (reportNow3.subtractDateTime(mimicPreSurveyAnswerDate, true).getDays());
    qunit.equal(secondRow3.surveyDaysUntilExpire, daysPreToPost1Expire3, "post survey 1 days until expire matches");
    let daysPreToLastExpire3 = NotificationSummaryData.daysPreToEndHardLimit - (reportNow3.subtractDateTime(mimicPreSurveyAnswerDate, true).getDays());

    let thirdRow3 = managementData3.toArray()[2];
    qunit.equal(thirdRow3.surveyName, "post survey 2", "status name matches");
    qunit.equal(thirdRow3.surveyStatus, "not allowed", "no pre-survey: post survey 2 status matches");
    qunit.equal(thirdRow3.surveyAnswerWorkDate, null, "no pre-survey: post survey 2 null answer date matches");
    qunit.equal(thirdRow3.surveyDaysUntilExpire, daysPreToLastExpire3, "no pre-survey: post survey 2 days until expire matches");
});

qunit.load();
qunit.start();
