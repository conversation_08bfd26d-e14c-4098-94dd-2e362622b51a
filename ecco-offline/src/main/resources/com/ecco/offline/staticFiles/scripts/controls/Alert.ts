import $ = require("jquery");
import {delay} from "@eccosolutions/ecco-common"
import BaseControl = require("./BaseControl");
import {WebApiError} from "@eccosolutions/ecco-common";

/**
 * Control which presents a dismissable alert by adding content to $(".alerts-container")
 *
 * Usage:
        var alert = Alert.dismissable("Your wish is granted!", "alert-success");
        alert.text("Sorry, I changed my mind").style("alert-warning");
 */
class Alert extends BaseControl {

    private $text: $.JQuery;
    private alertStyle: string;

    constructor(alertStyle = "alert-info") {
        var $element = $("<div>").addClass("alert alert-dismissable").addClass(alertStyle);
        super($element);
        this.alertStyle = alertStyle;

        var $button = $("<button>", {type: "button", "class": "close", "data-dismiss": "alert"})
            .append( $("<span>").attr("aria-hidden", "true").html("&times;") )
            .append( $("<span>").addClass("sr-only").text("Close") );

        this.$text = $("<span>");

        $element
            .append($button)
            .append(this.$text);
    }

    public style(alertStyle: string) {
        this.element().removeClass(this.alertStyle).addClass(alertStyle);
        this.alertStyle = alertStyle;
        return this;
    }

    public exception(error: any) {
        this.append( $("<p>").text(error.toString()) );
        if (error.stack) {
            var $stack = $("<pre>").text(error.stack).hide();
            this.append($("<button>")
                .addClass("btn btn-link btn-sm")
                .text("show details...")
                .click( () => $stack.toggle() )
            );
            this.append($stack);
        }
        return this;
    }


    public text(text: string) {
        this.$text.text(text);
        return this;
    }

    public html(html: string) {
        this.$text.html(html);
        return this;
    }

    public static dismissable(text: string, style = "alert-info"): Alert {
        var result = new Alert(style).text(text);
        var $container = $(".alerts-container");
        if ($container.length == 0) {
            alert(text);
        }
        else {
            $container.append( result.element() );
        }
        return result;
    }

    public static dismissableWithTimeout(text: string, style = "alert-info"): Alert {
        var result = new Alert(style).text(text);
        var $container = $(".alerts-container");
        if ($container.length == 0) {
            alert(text);
        }
        else {
            $container.append( result.element() );
            delay(2000)
                .then( () => {
                    result.element().fadeOut(2000, () => result.element().remove());
                });
        }
        return result;
    }

    public static error(text: string, error?: WebApiError) {
        var $container = $(".alerts-container");
        if ($container.length == 0) {
            alert(text + (error ? ": \n\n" + error.toString() : ""));
        }
        else {
            var result = new Alert("alert-warning")
                .text(text)
                .exception(error);
            $container.append( result.element() );
        }
    }
}
export = Alert
