package com.ecco.calendar.cosmo

import com.ecco.calendar.core.Attendee
import com.ecco.calendar.core.Recurrence
import com.ecco.calendar.core.Recurrence.RecurrenceHandle
import com.ecco.calendar.core.Recurrence.Status
import com.ecco.calendar.core.RecurringEntry.RecurringEntryHandle
import net.fortuna.ical4j.model.Property
import net.fortuna.ical4j.model.property.XProperty
import org.hibernate.Hibernate
import org.joda.time.DateTime
import org.osaf.cosmo.model.EventStamp
import org.osaf.cosmo.model.NoteOccurrence
import org.osaf.cosmo.model.StampUtils
import org.springframework.hateoas.Link
import org.springframework.hateoas.RepresentationModel
import java.net.URI
import java.util.stream.Collectors

/**
 * A Recurrence from a virtual, non-concrete, NoteOccurrence representing a recurrence of a recurring entry
 * @since 11/09/2013
 */
open class NoteOccurrenceToRecurrenceAdapter(noteOccurrence: NoteOccurrence,
                                             val converter: CosmoConverter
) : Recurrence, RepresentationModel<NoteItemToRecurrenceAdapter>() {

    private val eventStamp: EventStamp = StampUtils.getEventStamp(noteOccurrence)

    override val recurrenceHandle = RecurrenceHandle.fromString(noteOccurrence.uid)!!

    override val recurringEntryHandle: RecurringEntryHandle
        = RecurringEntryHandle.fromString(noteOccurrence.modificationUid.parentUid)

    override fun copyLinks(links: Iterable<Link>) {
        add(links)
    }

    // NoteOccurrence's are not concrete instances, so hold nothing different to the schedule
    override val modified: Boolean = false

    final override var managedBy: URI? = null

    final override var updatedBy: URI? = null

    override val title: String? = noteOccurrence.displayName

    final override val scheduledStart: DateTime = DateTime(noteOccurrence.occurrenceDate)

    override val start: DateTime = scheduledStart // Not modified, by definition

    override val end: DateTime = DateTime(eventStamp.duration?.getTime(noteOccurrence.occurrenceDate)) // ?? start

    override val status: Status? = if (eventStamp.status != null) Status.valueOf(eventStamp.status) else null

    override val attendees: Set<Attendee> get() {
        val propertyList = eventStamp.masterEvent.getProperties<net.fortuna.ical4j.model.property.Attendee>(Property.ATTENDEE)
        @Suppress("UNCHECKED_CAST")
        return (propertyList as List<net.fortuna.ical4j.model.property.Attendee?>)
            .stream().map { attendee: net.fortuna.ical4j.model.property.Attendee? -> AttendeeAdapter(attendee, converter.getCalendarIdFromAttendee(attendee)) }
            .collect(Collectors.toSet())
    }

    init {
        val stamp = StampUtils.getBaseEventStamp(noteOccurrence)
        val event = stamp.event
        if (event != null) {
            val managedBy = event.getProperty<XProperty>(CosmoConverter.MANAGED_BY_PROPERTY_NAME)
            if (managedBy != null && managedBy.value != null) {
                this.managedBy = URI.create(managedBy.value)
            }
            val updatedBy = event.getProperty<XProperty>(CosmoConverter.UPDATED_BY_PROPERTY_NAME)
            if (updatedBy != null && updatedBy.value != null) {
                this.updatedBy = URI.create(updatedBy.value)
            }
        }
        for (calendar in noteOccurrence.parents) {
            Hibernate.initialize(calendar.owner) // Call this now to initialise the collection.
        }
    }

    override fun toString(): String {
        return "recurrenceHandle=$recurrenceHandle, scheduledStart=$scheduledStart, start=$start, end=$end, status=$status, attendees=$attendees"
    }


}