package com.ecco.data.client.dataimport.support;

import com.ecco.webApi.contacts.WorkerViewModel;
import org.springframework.web.client.RestTemplate;

public class WorkerHandler extends AbstractHandler<WorkerViewModel> {
    private static final String apiPath = "/api/workers/";

    public WorkerHandler(RestTemplate template) {
        super(template);
    }

    @Override
    protected void processEntity(ImportOperation<WorkerViewModel> operation) {

        /* TODO this was a quick hack to show missing users
        String[] nameParts = StringUtils.split(operation.record.firstName);

        if (nameParts == null) {
            return;
        }
        if ((nameParts.length > 0) && (nameParts.length < 2)) {
            System.out.println("first/last " + nameParts[0] + ": SINGLE NAME");
        }
        if (nameParts.length == 2) {
            ResponseEntity<IndividualViewModel[]> vmsEntity = getForEntity(
                operation.baseUri + "/api/individuals/filter/byName/{firstName}/{lastName}/",
                IndividualViewModel[].class, nameParts[0], nameParts[1]);

            IndividualViewModel[] vms = vmsEntity.getBody();
            if (vms.length != 1)
            {
                System.out.println("first/last " + nameParts[0] + "/" + nameParts[1] + ": matched with: " + vms.length);
            }
        }
        */

        postAcceptingCreatedOrUnprocessableEntity(operation.baseUri + apiPath, operation.record);
    }
}
