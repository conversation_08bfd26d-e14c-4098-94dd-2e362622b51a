package com.ecco.webApi.serviceConfig;

import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.dto.ChangeViewModel;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.HashSet;

public class ProjectCommandViewModel extends BaseCommandViewModel {

    @Nullable
    public Long projectId;

    @Nonnull
    public String operation;

    @Nullable
    public ChangeViewModel<String> changeName;

    @Nullable
    public HashSet<String> ldapGroupsToAdd;

    @Nullable
    public HashSet<String> ldapGroupsToRemove;

    ProjectCommandViewModel() {
        super();
    }

    /**
     * @param operation "add" or "update"
     * @param serviceId null when doing add, required when doing update.
     * @param serviceTypeId required when doing add, null when doing update.
     */
    public ProjectCommandViewModel(String operation, Long projectId) {
        super(UriComponentsBuilder
                .fromUriString("config/project/")
                .buildAndExpand(projectId).toUriString());
        this.operation = operation;
        this.projectId = projectId;
    }

    public ProjectCommandViewModel(String operation, int projectId) {
        this(operation, Long.valueOf(projectId));
    }

    public ProjectCommandViewModel changeName(String from, String to) {
        this.changeName = ChangeViewModel.create(from, to);
        return this;
    }

}
