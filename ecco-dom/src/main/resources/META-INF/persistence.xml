<persistence xmlns="http://java.sun.com/xml/ns/persistence"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://java.sun.com/xml/ns/persistence http://java.sun.com/xml/ns/persistence/persistence_2_0.xsd"
    version="2.0">
    <persistence-unit name="eccoPU">
        <class>com.ecco.dom.ReferralActivityWorker</class>
        <class>com.ecco.dom.BaseComment</class>
        <class>com.ecco.dom.EvidenceActivity</class>
        <class>com.ecco.dom.EvidenceSupportComment</class>
        <class>com.ecco.dom.EvidenceSupportAnswer</class>
        <class>com.ecco.dom.EvidenceThreatAction</class>
        <class>com.ecco.dom.EvidenceSupportAction</class>
        <class>com.ecco.dom.EvidenceSupportWork</class>
        <class>com.ecco.dom.Review</class>
        <class>com.ecco.dom.CustomEventImpl</class>
        <class>com.ecco.dom.EvidenceAction</class>
        <class>com.ecco.dom.ServiceRecipientActivity</class>
        <class>com.ecco.dom.ReferralActivityType</class>
        <class>com.ecco.dom.EvidenceSupportOutcome</class>
        <class>com.ecco.dom.EvidenceSupportFlag</class>
        <class>com.ecco.evidence.dom.Signature</class>
        <class>com.ecco.dom.EvidenceThreatWork</class>
        <class>com.ecco.dom.Contact_Event</class>
        <class>com.ecco.dom.EvidenceThreatComment</class>
        <class>com.ecco.dom.EvidenceRisk</class>
        <class>com.ecco.dom.CustomEventWithServiceRecipient</class>
        <class>com.ecco.dom.ClientDetailAbstract</class>
        <class>com.ecco.dom.ReferralServiceRecipient</class>
        <class>com.ecco.dom.Referral</class>
        <class>com.ecco.dom.EvidenceThreatFlag</class>
        <class>com.ecco.dom.EvidenceOutcome</class>
        <class>com.ecco.dom.SignpostComment</class>
        <class>com.ecco.dom.ReferralBaseComment</class>
        <class>com.ecco.dom.ClientDetail</class>
        <class>com.ecco.dom.EvidenceComment</class>
        <class>com.ecco.dom.EvidenceThreatOutcome</class>
        <class>com.ecco.dom.Agency</class>
        <class>com.ecco.dom.ExitComment</class>
        <class>com.ecco.dom.Contact_Event_MultiId</class>
        <class>com.ecco.dom.LocalAuthority</class>
        <class>com.ecco.dom.EvidenceAnswer</class>
        <class>com.ecco.dom.agreements.ServiceAgreement</class>
        <class>com.ecco.dom.agreements.AppointmentSchedule</class>
        <class>com.ecco.dom.agreements.DemandScheduleDirectTask</class>
        <class>com.ecco.dom.agreements.AppointmentType</class>
        <class>com.ecco.dom.agreements.DaysOfWeek</class>
        <class>com.ecco.dom.ClickStream</class>
    </persistence-unit>
</persistence>
