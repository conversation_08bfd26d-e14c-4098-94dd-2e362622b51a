# ui selenium tests now disabled on github
name: ui-tests
on:
  schedule:
    - cron: "00 20 * * THU" # Build takes 75 mins, so daily per month is 2250 * 0.008 = $18. One day = < $3
  workflow_dispatch:
    inputs:
      MAVEN_ARGS:
        description: 'Maven args'
        required: false
        default: '-Dtest=x'
env:
  BROWSER: FIREFOX
  #BROWSER: CHROME
  #SELENIUM_VERSION_ARG: -Dselenium.version=4.0.0 # If want to override
  HOST_URL: http://localhost:8082/ecco-war
  TEST_RETRIES: 2
  # Multi-threaded builds currently dies in acceptance tests
  MVN_THREADS: -T1
  # Use MAVEN_ARGS: -Dtest={JavaClassName} to run just a single test
  # skip tests with MAVEN_ARGS: "-Dtest=x"
  MAVEN_OPTS: -Xmx1280M  -Dmaven.wagon.httpconnectionManager.ttlSeconds=25 -Dmaven.wagon.http.retryHandler.count=3
  NX_BRANCH: ${{github.event.number}}
  NX_RUN_GROUP: ${{github.run_id}}
jobs:
  parameters:
    name: Determine parameters
    runs-on: ubuntu-latest
    outputs:
      build-and-test-name: ${{steps.build-and-test.outputs.name}}
      test-report-name: ${{steps.build-and-test.outputs.test-report-name}}
      acceptance-test-profiles: ${{steps.build-and-test.outputs.acceptance-test-profiles}}
    steps:
      - name: Determine build and test parameters
        id: build-and-test
        run: |
          ref="${{github.ref}}"
          echo "MAVEN_ARGS: ${{github.event.inputs.MAVEN_ARGS}}"
          echo "name=Build and Smoke Test" >> $GITHUB_OUTPUT
          echo "test-report-name=Smoke Test Report" >> $GITHUB_OUTPUT
          echo "acceptance-test-profiles=-Penable-smoke-tests -DskipITs" >> $GITHUB_OUTPUT
  log-build-parameters:
    name: Log build parameters
    runs-on: ubuntu-latest
    needs: parameters
    steps:
      - name: Log build parameters
        run: |
          echo "::stop-commands::yDvCQrxk"
          cat <<"EOF"
          ${{toJson(needs.parameters.outputs)}}
          EOF
          echo "::yDvCQrxk"
  build-and-test:
    name: ${{needs.parameters.outputs.build-and-test-name}}
    runs-on: ubuntu-latest
    needs: parameters
    steps:
      - name: Checkout
        uses: actions/checkout@v2.3.3
        with:
          fetch-depth: 50
      - name: Set up OpenJDK 17
        uses: actions/setup-java@v1.4.3
        with:
          java-version: 17
      - name: Cache Maven dependencies
        uses: actions/cache@v2.1.1
        with:
          path: ${{env.HOME}}/.m2
          key: ${{runner.os}}-m2-${{hashFiles('**/pom.xml')}}
          restore-keys: ${{runner.os}}-m2-
      - name: Find yarn cache
        id: find-yarn-cache
        run: echo "dir=$(yarn cache dir)" >> $GITHUB_OUTPUT
      - name: Cache yarn dependencies
        uses: actions/cache@v2.1.1
        with:
          path: ${{steps.find-yarn-cache.outputs.dir}}
          key: ${{runner.os}}-yarn-${{hashFiles('**/yarn.lock')}}
          restore-keys: ${{runner.os}}-yarn-
      - name: Configure access token for GitHub Packages
        run: echo "//npm.pkg.github.com/:_authToken=${{secrets.PERSONAL_ACCESS_READ}}" > ~/.npmrc
      - name: Fetch main branch so nx can compare changes
        if: ${{ github.ref != 'refs/heads/main' }}
        run: git fetch --quiet --no-tags --prune --shallow-since 2020-12-31 origin main:main
      - name: Deepen this branch so nx can compare changes
        if: ${{ github.ref != 'refs/heads/main' }}
        run: git fetch --quiet --no-tags --prune --shallow-since 2020-12-31 origin ${{github.ref}}
      - name: mvn verify
        timeout-minutes: 120
        run: |
          xvfb-run --server-args="-ac -screen 0 1920x1080x24" --auto-servernum \
            mvn verify --batch-mode ${MVN_THREADS} -Dbrowser="${BROWSER}" \
            ${{needs.parameters.outputs.acceptance-test-profiles}} \
            -Dtests.retries="${TEST_RETRIES}" -Dspring.mail.host=smtp.mailtrap.io \
            -Dspring.mail.username=3de4c1c0de758c -Dspring.mail.password=6c0f8db0b5ddc8 \
            -Denv=dev -Ddb=h2 -Dliquibase=CREATE \
            -DfailIfNoTests=false --fail-at-end  -Ddeploy.server=local ${SELENIUM_VERSION_ARG} \
            -Dtarget.host.url="${HOST_URL}" ${{github.event.inputs.MAVEN_ARGS}}
      - name: Report ecco-acceptance-tests results
        if: always()
        uses: EnricoMi/publish-unit-test-result-action@v2.7.0
        with:
          github_token: ${{secrets.GITHUB_TOKEN}}
          check_name: ${{needs.parameters.outputs.test-report-name}}
          files: "ecco-acceptance-tests/target/surefire-reports/TEST*.xml"
      - name: Upload Acceptance Tests logs to logs artifact
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: logs
          path: ecco-acceptance-tests/logs/*
          if-no-files-found: ignore
      - name: Upload Acceptance Tests snapshots to logs artifact
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: logs
          path: ecco-acceptance-tests/target/surefire-reports/*.png
          if-no-files-found: ignore
      - name: Upload Ecco logs to logs artifact
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: logs
          path: ecco/logs/ecco.log
          if-no-files-found: ignore
      - name: Report build failure on Slack
        if: failure()
        uses: 8398a7/action-slack@v3
        with:
          author_name: ${{needs.parameters.outputs.build-and-test-name}}
          job_name: ${{needs.parameters.outputs.build-and-test-name}}
          status: ${{job.status}}
          fields: repo,job,ref,commit,message,author,took
        env:
          SLACK_WEBHOOK_URL: ${{secrets.SLACK_WEBHOOK_URL}}
          MATRIX_CONTEXT: ${{toJson(matrix)}}
  upload-logs:
    name: Upload logs of failed build and test
    runs-on: ubuntu-latest
    needs:
      - build-and-test
    if: failure()
    steps:
      - name: Download build logs
        uses: actions/download-artifact@v4
        with:
          name: logs
      - name: Upload logs
        uses: garygrossgarten/github-action-scp@v0.6.0
        with:
          local: .
          remote: /home/<USER>/github-actions-failure/
          host: 732939-www2.eccosolutions.co.uk
          port: 7010
          username: ${{secrets.DEPLOY_USER}}
          password: ${{secrets.DEPLOY_PASS}}
