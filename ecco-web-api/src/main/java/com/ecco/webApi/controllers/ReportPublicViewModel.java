package com.ecco.webApi.controllers;

import lombok.Data;

@Data
public class ReportPublicViewModel {

    /**
     * Number of referrals across our service (across all projects) = new referrals.
     * Rolling year to date.
     */
    public int referralsIn;

    /**
     * How many people have accessed our service (across all projects) = referrals 'accept on service'.
     * Rolling year to date.
     */
    public int referralsLive;

    /**
     * How many people have moved on from our service = exited referrals
     * Rolling year to date.
     */
    public int referralsOut;

    /**
     * Number of SMART steps (achieved in the last month) = smart steps achieved.
     * Rolling month to date.
     */
    public int actionsAchieved;

    /**
     * How many tasks have been achieved. Rolling month to date.
     */
    public int tasksAchieved;

    /**
     * The primary need and percent. Rolling month to date.
     */
    public String primaryNeed;
    public int primaryNeedPercent;

    /**
     * The secondary need and percent. Rolling month to date.
     */
    public String secondaryNeed;
    public int secondaryNeedPercent;

}
