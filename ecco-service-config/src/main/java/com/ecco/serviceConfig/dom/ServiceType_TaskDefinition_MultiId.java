package com.ecco.serviceConfig.dom;

import static lombok.AccessLevel.PROTECTED;

import java.io.Serializable;

import javax.persistence.Embeddable;
import javax.persistence.EntityManager;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;

import org.springframework.util.Assert;

import lombok.*;

@Embeddable
@NoArgsConstructor(access = PROTECTED)
@Getter
@Setter
@EqualsAndHashCode
public class ServiceType_TaskDefinition_MultiId implements Serializable {

    private static final long serialVersionUID = 1L;

    @ManyToOne(fetch=FetchType.LAZY, optional=false)
    private TaskDefinition taskDefinition;

    @ManyToOne(fetch=FetchType.LAZY, optional=false)
    private ServiceTypeMinimalView serviceType;

    public ServiceType_TaskDefinition_MultiId(ServiceTypeMinimalView serviceType, TaskDefinition taskDefinition) {
        this.taskDefinition = taskDefinition;
        this.serviceType = serviceType;
    }

    public void setServiceType(ServiceTypeMinimalView serviceType) {
        Assert.notNull(serviceType.getId());
        this.serviceType = serviceType;
    }

    public static ServiceType_TaskDefinition_MultiId create(EntityManager entityManager, long serviceTypeId, long taskDefinitionId) {
        // Make sure to use proper entity references. If you just create dummy objects with the IDs set, when the real
        // object also exists in the session, you risk getting a NonUniqueObjectException.
        // See https://hibernate.atlassian.net/browse/HHH-9521
        return new ServiceType_TaskDefinition_MultiId(
                entityManager.getReference(ServiceTypeMinimalView.class, serviceTypeId),
                entityManager.getReference(TaskDefinition.class, taskDefinitionId)
        );
    }
}
