import {bus} from "@eccosolutions/ecco-common";
import * as commands from "ecco-commands";
import * as dto from "ecco-dto/evidence-dto";


export class GoalTransientStatusEvent {
    public static bus = bus<GoalTransientStatusEvent>();

    constructor(public actionDefId: number, public statusTo: dto.SmartStepStatus) {
    }
}

/** A change of state on the smartstep (except for the chain/link/association)
 * Currently used to show a menu when the smart step is activated
 */
export class GoalTouchedEvent {
    public static bus = bus<GoalTouchedEvent>();

    constructor(public actionInstanceUuid: string) {
    }
}

export class GoalUpdateEvent {
    public static bus = bus<GoalUpdateEvent>();

    constructor(public command: commands.GoalUpdateCommand, public commandDto: dto.GoalUpdateCommandDto) {
    }
}

export class RiskUpdateEvent {
    public static bus = bus<RiskUpdateEvent>();

    constructor(public command: any /* TODO: RiskUpdateCommand*/) {
    }
}

// MOVED
// QuestionAnswerTransientEvent

export class QuestionAnswerUpdateEvent {
    public static bus = bus<QuestionAnswerUpdateEvent>();

    constructor(public command: commands.QuestionAnswerCommand, public commandDto: dto.QuestionAnswerCommandDto) {
    }
}
