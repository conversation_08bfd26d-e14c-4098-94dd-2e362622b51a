package com.ecco.webApi.serviceConfig;

import com.ecco.serviceConfig.viewModel.ProjectViewModel;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
public class ServiceViewModel {

    /** Ignored when POSTing a new entity */
    public Integer id;

    public String name;

    // we need read/write as an object because the same view model is used in generate and receive (api tests)
    // so we restore to an object to solve the original problem - NPE when setting this field from 'allocate to'
    // (NB this class is a clone)
    public Long serviceTypeId;

    public List<ProjectViewModel> projects;

    public Map<String, Object> parameters;
    public Boolean disabled;

}
