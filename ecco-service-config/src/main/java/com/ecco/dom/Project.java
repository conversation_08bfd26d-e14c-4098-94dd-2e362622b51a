package com.ecco.dom;

import com.ecco.infrastructure.entity.AbstractLongKeyedEntity;
import com.ecco.dom.contacts.Address;
import lombok.*;

import javax.annotation.Nullable;
import javax.persistence.*;

@Entity
@Table(name="projects")
@Getter
@Setter
@NoArgsConstructor(access = AccessLevel.PUBLIC)
public class Project extends AbstractLongKeyedEntity implements MutableIdName<Long> {

    private static final long serialVersionUID = 1L;

    @Column
    String name;

//    @ManyToMany(fetch=FetchType.LAZY)
//    @JoinTable(name = "services_projects", joinColumns = @JoinColumn(name = "projectId"), inverseJoinColumns = @JoinColumn(name = "serviceId"))
//    Set<Service> services;

    @Embedded
    Address address;

    @ManyToOne(cascade = {}, fetch = FetchType.EAGER)
    @JoinColumn(name = "regionId")
    Region region;

    // TODO capacity at a project would represent a hard limit, but the capacity should really
    // be on the service/project combination? - but this works for demoware for now
    int capacity = 0;

    /** Id of the building that this is equivalent to - can't see as it's another bounded domain
     * This may just be temporary as we migrate projects with accommodation to buildings, and can delete
     * when accommodation field is removed */
    @Nullable
    @Column
    Integer buildingId;

    public Project(Long id) {
        this.setId(id);
    }

    public Project(String name) {
        this.name = name;
    }

    public Project(long id, String name) {
        this.setId(id);
        this.name = name;
    }
}
