package com.ecco.webApi.contacts;

import java.util.function.Function;

import javax.annotation.Nullable;

import com.ecco.buildings.repositories.AddressRepository;
import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.dom.AddressedLocation;
import com.ecco.dom.Agency;
import com.ecco.dom.contacts.Address;
import com.ecco.webApi.contacts.address.AddressFromViewModel;
import org.apache.commons.lang.StringUtils;

public class AgencyFromViewModel implements Function<AgencyViewModel, Agency> {

    private static final AddressFromViewModel addressFromViewModel = new AddressFromViewModel();

    private final AddressRepository addressRepository;

    public AgencyFromViewModel(AddressRepository addressRepository) {
        super();
        this.addressRepository = addressRepository;
    }

    @Override
    @Nullable
    public Agency apply(@Nullable AgencyViewModel input) {
        if (input == null) {
            throw new NullPointerException("input AgencyViewModel must not be null");
        }

        Agency agency = new Agency();
        AgencyFromViewModel.apply(input, agency, addressRepository);
        return agency;
    }

    public static void apply(AgencyViewModel input, Agency agency, AddressRepository addressRepository) {
        /*if (input.agencyCategoryNew != null) {
            agency.setAgencyCategoryId(findOrCreateAgencyCategory(input, null));
        }*/
        agency.setCode(input.code);
        agency.setCompanyName(input.companyName);
        agency.setOutOfArea(input.outOfArea);
        agency.setEmail(StringUtils.trimToNull(input.email));
        agency.setPhoneNumber(input.phoneNumber);
        agency.setAgencyCategoryId(input.agencyCategoryId);
        agency.setArchived(input.getArchived());
        if(input.address != null) {
            agency.setAddress(addressFromViewModel.apply(input.address));
        }

        if (input.addressedLocationId != null) {
            AddressedLocation addressedLocation = addressRepository.findById(input.addressedLocationId).orElseThrow(NullPointerException::new);
            agency.setAddressedLocation(addressedLocation);
            agency.setAddress(Address.from(addressedLocation)); // see ContactImpl.AddressLike
        }

    }

    private static Integer findOrCreateAgencyCategory(AgencyViewModel input, ListDefinitionRepository listDefinitionRepository) {
        //var found = listDefinitionRepository.findByListNameAndName("agencyCategory", input.agencyCategoryNew);
        //return found != null ? found : agencyCategoryRepository.save(new AgencyCategory(input.agencyCategory));
        throw new UnsupportedOperationException("Needs re-implementing");
    }
}
