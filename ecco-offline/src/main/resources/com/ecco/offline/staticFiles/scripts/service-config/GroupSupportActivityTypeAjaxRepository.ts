import {ApiClient} from "ecco-dto";
import {ActivityType} from "ecco-dto/service-config-dto";
import {GroupSupportActivityTypeRepository} from "./GroupSupportActivityTypeRepository";

export class GroupSupportActivityTypeAjaxRepository implements GroupSupportActivityTypeRepository {
    constructor(private apiClient: ApiClient) {
    }

    public findAllActivityTypes(): Promise<ActivityType[]> {
        return this.apiClient.get<ActivityType[]>("activityTypes/");
    }

    public findActivityTypesByServiceId(serviceId: number): Promise<ActivityType[]> {
        var path = `service/${serviceId}/activityTypes/`;

        return this.apiClient.get<ActivityType[]>(path);
    }


    // NOTE: Implemented matches GoalCommandController
    public findActivityInterestsByServiceRecipientId(serviceRecipientId: number): Promise<ActivityType[]> {
        return this.apiClient.get<ActivityType[]>(`service-recipients/${serviceRecipientId}/activityInterest/`);
    }
}
