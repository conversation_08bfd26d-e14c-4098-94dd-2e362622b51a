package com.ecco.data.client.dataimport.csv;

import static org.hamcrest.Matchers.is;
import static org.junit.Assert.assertThat;

import org.joda.time.LocalDate;
import org.junit.Test;

public class LocalDateFromStringConverterTest {

    private LocalDateFromStringConverter converter = new LocalDateFromStringConverter();

    @Test
    public void shouldTurnYear23into1923() {
        LocalDate date = converter.convert("13/11/23");
        assertThat(date.getYear(), is(1923));
        assertThat(date.getMonthOfYear(), is(11));
        assertThat(date.getDayOfMonth(), is(13));
    }

    @Test
    public void shouldTurnYear19into2019() {
        LocalDate date = converter.convert("13/11/19");
        assertThat(date.getYear(), is(2019));
        assertThat(date.getMonthOfYear(), is(11));
        assertThat(date.getDayOfMonth(), is(13));
    }

    @Test
    public void shouldTurnYear2019into2019() {
        LocalDate date = converter.convert("13/11/2019");
        assertThat(date.getYear(), is(2019));
        assertThat(date.getMonthOfYear(), is(11));
        assertThat(date.getDayOfMonth(), is(13));
    }
}
