<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd">

    <changeSet context="1.1-base-data,test-data" author="baseline" id="insert-setting-base-data">
        <preConditions onFail="MARK_RAN">
            <!-- Was previously this changeset, so only execute if not already done as that -->
            <not>
                <changeSetExecuted author="baseline" id="1366042568880-45"
                    changeLogFile="classpath:sql/1.1-baseline/portableDataBaseline.xml" />
            </not>
        </preConditions>

        <insert tableName="setting">
            <column name="id" valueNumeric="1"/>
            <column name="version" valueNumeric="1"/>
            <column name="keyname" value="ENABLED"/>
            <column name="namespace" value="com.ecco.authn.ad"/>
            <column name="readOnStartup" valueBoolean="true"/>
            <column name="keyvalue" value="false"/>
        </insert>
        <insert tableName="setting">
            <column name="id" valueNumeric="2"/>
            <column name="version" valueNumeric="1"/>
            <column name="keyname" value="DOMAIN"/>
            <column name="namespace" value="com.ecco.authn.ad"/>
            <column name="readOnStartup" valueBoolean="true"/>
            <column name="keyvalue" value="example.com"/>
        </insert>
        <insert tableName="setting">
            <column name="id" valueNumeric="3"/>
            <column name="version" valueNumeric="1"/>
            <column name="keyname" value="LDAP_URL"/>
            <column name="namespace" value="com.ecco.authn.ad"/>
            <column name="readOnStartup" valueBoolean="true"/>
            <column name="keyvalue" value="ldap://dc1.example.com/"/>
        </insert>
        <insert tableName="setting">
            <column name="id" valueNumeric="4"/>
            <column name="version" valueNumeric="1"/>
            <column name="keyname" value="ENABLED"/>
            <column name="namespace" value="com.ecco.authn.kerberos"/>
            <column name="readOnStartup" valueBoolean="true"/>
            <column name="keyvalue" value="false"/>
        </insert>
        <insert tableName="setting">
            <column name="id" valueNumeric="5"/>
            <column name="version" valueNumeric="1"/>
            <column name="keyname" value="SERVICE_PRINCIPAL"/>
            <column name="namespace" value="com.ecco.authn.kerberos"/>
            <column name="readOnStartup" valueBoolean="true"/>
            <column name="keyvalue" value="HTTP/<EMAIL>"/>
        </insert>
        <insert tableName="setting">
            <column name="id" valueNumeric="6"/>
            <column name="version" valueNumeric="1"/>
            <column name="keyname" value="SHARED_SECRET_KEYTAB_LOCATION"/>
            <column name="namespace" value="com.ecco.authn.kerberos"/>
            <column name="readOnStartup" valueBoolean="true"/>
            <column name="keyvalue" value="file:/some/folder/http-web.keytab"/>
        </insert>
        <insert tableName="setting">
            <column name="id" valueNumeric="7"/>
            <column name="version" valueNumeric="1"/>
            <column name="keyname" value="LDAP_USER_LOOKUP_ENABLED"/>
            <column name="namespace" value="com.ecco.authn.kerberos"/>
            <column name="readOnStartup" valueBoolean="true"/>
            <column name="keyvalue" value="false"/>
        </insert>
        <insert tableName="setting">
            <column name="id" valueNumeric="8"/>
            <column name="version" valueNumeric="1"/>
            <column name="keyname" value="LDAP_READ_USER_DN"/>
            <column name="namespace" value="com.ecco.authn.kerberos"/>
            <column name="readOnStartup" valueBoolean="true"/>
            <column name="keyvalue" value="<EMAIL>"/>
        </insert>
        <insert tableName="setting">
            <column name="id" valueNumeric="9"/>
            <column name="version" valueNumeric="1"/>
            <column name="keyname" value="LDAP_READ_USER_PASSWORD"/>
            <column name="namespace" value="com.ecco.authn.kerberos"/>
            <column name="readOnStartup" valueBoolean="true"/>
            <column name="keyvalue" value="s3cur3d"/>
        </insert>
        <insert tableName="setting">
            <column name="id" valueNumeric="10"/>
            <column name="version" valueNumeric="1"/>
            <column name="keyname" value="LDAP_READ_USER_SEARCH_BASE"/>
            <column name="namespace" value="com.ecco.authn.kerberos"/>
            <column name="readOnStartup" valueBoolean="true"/>
            <column name="keyvalue" value="CN=Users,DC=example,DC=com"/>
        </insert>
        <insert tableName="setting">
            <column name="id" valueNumeric="11"/>
            <column name="version" valueNumeric="1"/>
            <column name="keyname" value="LDAP_READ_USER_SEARCH_FILTER"/>
            <column name="namespace" value="com.ecco.authn.kerberos"/>
            <column name="readOnStartup" valueBoolean="true"/>
            <column name="keyvalue" value="(userPrincipalName={0}@example.com)"/>
        </insert>
        <insert tableName="setting">
            <column name="id" valueNumeric="12"/>
            <column name="version" valueNumeric="1"/>
            <column name="keyname" value="LDAP_URL"/>
            <column name="namespace" value="com.ecco.authn.kerberos"/>
            <column name="readOnStartup" valueBoolean="true"/>
            <column name="keyvalue" value="ldap://dc1.example.com/"/>
        </insert>
        <insert tableName="setting">
            <column name="id" valueNumeric="13"/>
            <column name="version" valueNumeric="1"/>
            <column name="keyname" value="SITE_TITLE"/>
            <column name="namespace" value="com.ecco"/>
            <column name="readOnStartup" valueBoolean="false"/>
            <column name="keyvalue" value=" "/>
        </insert>
    </changeSet>

</databaseChangeLog>
