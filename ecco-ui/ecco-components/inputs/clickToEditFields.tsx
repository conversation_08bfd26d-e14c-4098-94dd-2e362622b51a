/* tslint:disable:no-console */
import {createSty<PERSON>, Icon, IconButton, InputAdornment, makeStyles, TextField, Theme} from "@eccosolutions/ecco-mui";
import * as React from "react";
import {useRef, useState} from "react";

const useFieldStyles = makeStyles((theme: Theme) => // NOTE: Must always have root:
    createStyles({
        root: {
            marginTop: theme.spacing(2),
            marginBottom: theme.spacing(2),
            display: "block"
        }
    })
);
const useInputStyles = makeStyles((theme: Theme) => // NOTE: Must always have root:
    createStyles({
        root: {},
        disabled: {
            border: 1,
            color: theme.palette.primary.main,
            // color: theme.palette.primary.light,
        }
    })
);

export const ClickToEditTextField = (props: {
    value: string | undefined | null,
    onChange: (value: string | null) => void
    label?: string
    type?: string
    className?: string;
}) => {

    const [value, setValue] = useState<string | null>(props.value ?? null);
    const [editing, setEditing] = useState(false);
    const ref = useRef<HTMLInputElement>();

    function cancel(e: React.MouseEvent | React.KeyboardEvent) {
        e.stopPropagation();
        setValue(props.value ?? null);
        setEditing(false);
    }

    function save(e: React.MouseEvent | React.KeyboardEvent) {
        e.stopPropagation();
        setEditing(false);
        props.onChange(value);
    }

    const classes = useFieldStyles();
    const inputClasses = useInputStyles();

    return <>
        <TextField
            classes={classes}
            className={props.className}
            inputRef={ref}
            label={props.label}
            InputProps={{
                classes: inputClasses,
                endAdornment: editing ? <>
                <InputAdornment position="end">
                    <IconButton key="save" aria-label="save" onClick={save}><Icon className="fa fa-check-circle-o"/></IconButton>
                    <IconButton key="cancel" aria-label="cancel" onClick={cancel}><Icon className="fa fa-times-circle-o"/></IconButton>
                </InputAdornment>
                </> : undefined
            }}
            InputLabelProps={{
                shrink: true,
            }}
            value={value ? value : undefined} // To prevent null being passed in which React moans about
            type={props.type}
            disabled={!editing}
            helperText={editing && "Enter to save"}
            onClick={
                () => {
                    setEditing(true);
                    setTimeout(() => {
                        ref.current?.focus();
                    },
                               100);
                }
            }
            onChange={e => setValue(e.target.value)}
            onKeyDown={e => {
                if (e.key === "Enter" || e.key === "Tab") {
                    save(e);
                } else if (e.key === "Escape") {
                    cancel(e);
                    e.preventDefault(); // FIXME: doesn't prevent Esc closing the modal
                }
                // console.log(e.key);
            }}
            // onBlur={DON'T} // This causes cancel to be called when clicking icons
        />
    </>;
};
