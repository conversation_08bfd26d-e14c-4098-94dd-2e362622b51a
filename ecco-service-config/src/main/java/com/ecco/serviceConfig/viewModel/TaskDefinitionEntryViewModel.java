package com.ecco.serviceConfig.viewModel;

import java.util.Map;

public class TaskDefinitionEntryViewModel {

    public static final String TASK_REFERALVIEW = "referralView";
    public static final long REFERRAL_VIEW = 18L;

    public long taskDefId;

    /** This is the unique name of the variant of the step (only one referral aspect exists with this name,
     * and it will usually relate to a different form, such as sourceWithIndividual vs source) */
    public String name;

    public Map<String, String> settings;

    /** Per outcome settings that override settings */
    public Map<Long, Map<String, String>> outcomeSettings;


    /** Non-sequential sort order value - do not try using it as task index */
    public int orderby; // avoid 'order' since its a keyword

    /** Zero-based index of this task when tasks are sorted by orderby */
    public int taskDefIndex;

    /** The schedule for this task (currently expected to be an integer for days)
     * to create due dates from the point the task is created */
    public String dueDateSchedule;

    public boolean allowNext; // allow the next aspect to be active
}
