package com.ecco.config.repositories;

import com.ecco.config.dom.ExternalSystem;
import org.springframework.data.repository.Repository;

import java.util.List;

/**
 * @since 09/07/2014
 */
public interface ExternalSystemRepository extends Repository<ExternalSystem, String> {

    List<ExternalSystem> findByBuildingSourceTrue();

    List<ExternalSystem> findByBuildingSinkTrue();

    List<ExternalSystem> findByClientSourceTrue();

    List<ExternalSystem> findByClientSinkTrue();

    List<ExternalSystem> findByStaffSourceTrue();

    List<ExternalSystem> findByStaffSinkTrue();

    List<ExternalSystem> findByDefaultEntryTrue();

}
