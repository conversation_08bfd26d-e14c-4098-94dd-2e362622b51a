
import NodeProxy = require("./NodeProxy");
import {StringUtils} from "@eccosolutions/ecco-common";
import GraphContext = require("../../evidence/graph/GraphContext");
import {OutcomeStarArmDefData, StarCommandFactory} from "../../evidence/star/domain";
import {DynamicTreeNode} from "../../draw/dynamic-tree";
import OutcomeStarScoreNodeWrapper = require("../../evidence/star/OutcomeStarScoreNodeWrapper");

// mimic OutcomeNode
class OutcomeStarArmNodeWrapper implements NodeProxy {

    private node: DynamicTreeNode;
    private scoreNodes: OutcomeStarScoreNodeWrapper[] = [];

    constructor(private outcomeStarArmDefData: OutcomeStarArmDefData,
                private context: GraphContext,
                private commandFactory: StarCommandFactory) {
        this.node = new DynamicTreeNode(StringUtils.wrapString(outcomeStarArmDefData.name || "", 15));
        this.node.addClickEventHandler( (event) => { this.clickedScoreElement();} );
        this.buildScoreNodes();
    }

    private clickedScoreElement() {
        console.log("NODE clicked: " + this.node.getCaption()); // Adding score could be done here
    }

    private buildScoreNodes() {
        this.outcomeStarArmDefData.visualAnswers.forEach(displayAnswer => {
            let node = new OutcomeStarScoreNodeWrapper(this.outcomeStarArmDefData,
                displayAnswer,
                this.context,
                this.commandFactory
            );
            this.node.addChild(node.getNode());
            this.scoreNodes.push(node);
        });
    }

    public updateArmAnswerVisuals(answer: string) {
        this.scoreNodes.forEach((scoreNode => scoreNode.updateArmAnswerVisuals(answer)));
    }

    public progressArmAnswerVisuals(answer: string) {
        this.scoreNodes.forEach((scoreNode => scoreNode.progressArmAnswerVisuals(answer)));
    }

    public getNode(): DynamicTreeNode {
        return this.node;
    }
}
export = OutcomeStarArmNodeWrapper;
