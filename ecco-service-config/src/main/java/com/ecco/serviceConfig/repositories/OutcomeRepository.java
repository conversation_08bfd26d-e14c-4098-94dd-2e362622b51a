package com.ecco.serviceConfig.repositories;

import com.ecco.serviceConfig.dom.Outcome;
import com.ecco.serviceConfig.dom.OutcomeService;
import com.ecco.serviceConfig.dom.OutcomeSupport;
import com.ecco.serviceConfig.dom.OutcomeThreat;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository for Outcomes of which there is only one
 */
public interface OutcomeRepository extends JpaRepository<Outcome, Long> {

    @Modifying
    @Query(nativeQuery = true,
            value = "UPDATE outcomes"
            + " SET name = ?1"
            + " WHERE id = ?2")
    void changeName(String to, long outcomeId);

    @Query("FROM OutcomeService ORDER BY id")
    List<OutcomeService> findAllService();

    @Query("FROM OutcomeSupport ORDER BY id")
    List<OutcomeSupport> findAllSupport();

    @Query("FROM OutcomeSupport where uuid=?1")
    OutcomeSupport findOneNeedByUuid(UUID uuid);

    @Query("FROM OutcomeThreat where uuid=?1")
    OutcomeThreat findOneThreatByUuid(UUID uuid);

    Outcome findOneByUuid(UUID uuid);

    OutcomeSupport findOneByName(String name);

    @Query("FROM OutcomeThreat ORDER BY id")
    List<OutcomeThreat> findAllThreat();

    @CacheEvict(cacheNames={"outcomeViewModels", "riskAreaViewModels"}, allEntries=true)
    @Override
    Outcome save(Outcome entity);

    Optional<Outcome> findById(long id);
}
