package com.ecco.data.client.actors;

import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import com.ecco.webApi.viewModels.DateTimeUpdateCommand;
import com.ecco.webApi.viewModels.Result;

public class UpdateAuditTimestampCommandActor extends BaseActor {

    public UpdateAuditTimestampCommandActor(RestTemplate restTemplate) {
        super(restTemplate);
    }

    /**
     * Method to allow us to override a system generated date, for testing.
     * In a command style, but really this is just an api call.
     */
    public ResponseEntity<Result> dateTimeUpdateReferral(long referralId, String path, DateTimeUpdateCommand dto) {
        final UriComponents uri = UriComponentsBuilder.fromHttpUrl(apiBaseUrl +
                "referrals/{id}/dateTimeUpdate/{path}/")
                .buildAndExpand(referralId, path);
        ResponseEntity<Result> response = restTemplate.postForEntity(uri.toUri(), dto, Result.class);
        return response;
    }
}
