package com.ecco.webApi.incidents;

import com.ecco.dom.incidents.IncidentServiceRecipient;
import com.ecco.webApi.controllers.CreateServiceRecipientCommandViewModel;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Nonnull;

public class CreateIncidentCommandViewModel extends CreateServiceRecipientCommandViewModel {

    private IncidentViewModel incidentViewModel;

    /** only for Cglib/Hibernate etc */
    @Deprecated
    protected CreateIncidentCommandViewModel() {
        super();
    }

    public CreateIncidentCommandViewModel(@Nonnull IncidentViewModel incidentViewModel) {
        super(UriComponentsBuilder
                .fromUriString("service-recipient/command/create/"+ IncidentServiceRecipient.PREFIX+"/")
                .toUriString(), IncidentServiceRecipient.PREFIX);
        this.incidentViewModel = incidentViewModel;
    }

    public IncidentViewModel getIncidentViewModel() {
        return incidentViewModel;
    }

}
