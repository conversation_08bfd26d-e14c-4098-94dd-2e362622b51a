import {<PERSON>, CardA<PERSON>, CardHeader} from "@eccosolutions/ecco-mui";
import {TaskNames} from "ecco-dto";
import EvidenceDelegatingForm from "../evidence/EvidenceDelegatingForm";
import AttachmentsHistory from "../evidence/history/AttachmentsHistory";
import * as React from "react";
import {Col} from "react-bootstrap";
import {useRouteMatch} from "react-router";
import ReactDom = require("react-dom");

/**
 * LOAD FORM - non-jsx, jsp
 */
/* Allow for non-jsx calls */
export function attachmentsEnhance($element: $.JQuery, srId: number) {
    ReactDom.render(
        <AttachmentsControl srId={srId}/>,
        $element[0]);
}

interface Props extends React.ClassAttributes<typeof AttachmentsControl> {
    srId?: number;
}


interface AttachmentDto {
    fileId: number;
    fileName: string
}

// NB usused
class AttachmentComponent extends React.Component<{renderComponent: React.ComponentClass<{attachment: AttachmentDto}>; attachments: AttachmentDto[]}, {}> {
    render() {
        return <div>{this.props.attachments.map(a =>
            <this.props.renderComponent attachment={a} key={`attachmentId${a.fileId}`}/>)
        }</div>
    }
}

// NB usused
function AttachmentItem(props: { attachment: AttachmentDto }) {
    return (
        <Col className="card" lg={3} md={4} sm={6} xs={12}>
            <Card style={{marginTop: 2, marginBottom: 2}}>
                <CardHeader
                    title={props.attachment.fileName}
                    subheader={props.attachment.fileId}
                />
                {/*{<div>any blurb</div>}*/}
                <CardActions>
                    <a key="key1" className="btn btn-link">download</a>
                </CardActions>
            </Card>
        </Col>
    );
}

function AttachmentsControl(props: Props) {
    const srId = props.srId ?? parseInt(useRouteMatch<{srId: string}>().params.srId)
    return (
        <>
            <div className="text-center page-header">
                <a key="newAttachment"
                   className="btn btn-link"
                   onClick={() => EvidenceDelegatingForm.showInReactModalByIds(srId, "new attachment",
                       TaskNames.needsAttachment, undefined)}
                >new attachment</a>
            </div>
            <AttachmentsHistory serviceRecipientId={srId}/>
        </>
    );
}
export default AttachmentsControl;
