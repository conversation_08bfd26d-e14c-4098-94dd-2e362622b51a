package com.ecco.webApi.contacts;

import java.util.function.Function;

import com.ecco.dom.Agency;
import com.ecco.dom.ContactImpl;
import com.ecco.dom.Individual;

public class ContactToViewModel implements Function<ContactImpl, ContactViewModel> {

    private final AgencyToViewModel agencyToViewModel;
    private final IndividualToViewModel individualToViewModel;


    public ContactToViewModel(AgencyToViewModel agencyToViewModel, IndividualToViewModel individualToViewModel) {
        this.agencyToViewModel = agencyToViewModel;
        this.individualToViewModel = individualToViewModel;
    }

    @Override
    public ContactViewModel apply(ContactImpl input) {
        if (input instanceof Individual) {
            return individualToViewModel.apply((Individual) input);
        }
        else if (input instanceof Agency) {
            return agencyToViewModel.apply((Agency) input);
        }
        throw new UnsupportedOperationException("Cannot convert class: " + input.getClass().getSimpleName());
    }
}
