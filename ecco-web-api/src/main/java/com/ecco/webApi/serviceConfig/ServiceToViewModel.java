package com.ecco.webApi.serviceConfig;

import com.ecco.dom.Service;
import com.ecco.serviceConfig.viewModel.ServiceCategorisationToProjectViewModel;
import lombok.RequiredArgsConstructor;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.Objects;
import java.util.function.Function;

import static java.util.stream.Collectors.toList;

@RequiredArgsConstructor
public final class ServiceToViewModel implements Function<Service, ServiceViewModel> {

    private final ServiceCategorisationToProjectViewModel serviceCategorisationToProjectViewModel;

    @Nonnull
    @Override
    public ServiceViewModel apply(@Nullable Service input) {
        if (input == null) {
            throw new NullPointerException("input Service must not be null");
        }

        ServiceViewModel result = new ServiceViewModel();
        result.id = input.getId() == null ? null : input.getId().intValue();
        result.name = input.getName();
        result.serviceTypeId = input.getServiceTypeId();
        result.projects = input.getCategorisations().stream().map(serviceCategorisationToProjectViewModel).filter(Objects::nonNull).collect(toList());
        result.parameters = input.getParameters();
        result.disabled = input.isDisabled();

        return result;
    }

}