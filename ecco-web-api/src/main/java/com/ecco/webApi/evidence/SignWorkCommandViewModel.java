package com.ecco.webApi.evidence;

import java.util.List;
import java.util.UUID;

import javax.annotation.Nonnull;

import org.joda.time.LocalDateTime;
import org.springframework.web.util.UriComponentsBuilder;

import com.ecco.dom.EvidenceGroup;
import com.ecco.dom.Individual;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * A command to annotate one or more items of Work with the service recipient's
 * signature, or with a signature captured on behalf of the service recipient.
 */
public final class SignWorkCommandViewModel extends BaseServiceRecipientCommandViewModel {

    /** Client generated UUID */
    //@Nonnull // TODO: can be uncommented once we've released 'see changes offline' to everyone
    public UUID signatureUuid;

    /** The evidence group for this operation */
    //@Nonnull // TODO: can be uncommented once we've released 'see changes offline' to everyone
    public String evidenceGroup;

    /**
     * The UUID of each piece of Work that shall be signed.
     */
    @Nonnull
    public List<UUID> workUuids;

    /**
     * SVG data representing the signature graphically.
     * <p/>
     * This is in the form of a complete SVG XML document.
     */
    @Nonnull
    public String svgXml;
    /**
     * Date and time when this signature was created.
     */
    @Nonnull
    public LocalDateTime signedDate;


    /**
     * @deprecated Do not use. Required by Hibernate/JPA.
     */
    @Deprecated
    public SignWorkCommandViewModel() {
    }

    public SignWorkCommandViewModel(int serviceRecipientId, @Nonnull List<UUID> workUuids,
            @Nonnull String svgXml, @Nonnull LocalDateTime signedDate) {
        super(UriComponentsBuilder
                .fromUriString("service-recipients/{serviceRecipientId}/evidence/{evidenceGroup}/{taskName}/signatures/")
                .buildAndExpand(serviceRecipientId, EvidenceGroup.NEEDS.nameAsLowercase(), "needsReduction")
                .toString(),
                serviceRecipientId);

        this.workUuids = workUuids;
        this.svgXml = svgXml;
        this.signedDate = signedDate;
    }
}
