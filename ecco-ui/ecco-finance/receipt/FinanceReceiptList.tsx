import {StringToObjectMap} from "@eccosolutions/ecco-common";
import {Box, Button, Grid, IconButton} from "@eccosolutions/ecco-mui";
import * as React from "react";
import {FC, useState} from "react";
import {EccoV3Modal, SchemaList, useServicesContext} from "ecco-components";
import {FinanceReceiptModal} from "./FinanceReceipt";
import {FinanceReceiptDto} from "ecco-dto/finance/finance-dto";
import {DeleteIcon} from "@eccosolutions/ecco-mui-controls";
import {EditIcon} from "@eccosolutions/ecco-mui-controls";
import {FinanceReceiptCommand} from "./finance-commands";

export const FinanceReceiptList: FC<{serviceRecipientId?: number}> = props => {

    const [search, setSearch] = useState<string | null>(null);
    const [page, setPage] = useState(0);
    const [filters, setFilters] = useState<StringToObjectMap<string[]>>({});
    const [editing, setEditing] = useState(false);
    const [deleting, setDeleting] = useState(false);
    const [receiptId, setReceiptId] = useState<number | undefined>();
    const [forceReload, setForceReload] = useState(0);
    const {getCommandRepository} = useServicesContext();

    const params = new URLSearchParams();
    params.append("r", forceReload.toString());
    if (props.serviceRecipientId) {
        params.append("srId", props.serviceRecipientId.toString());
    }
    if (search) params.append("search", search);
    if (page) params.append("page", page.toFixed());
    const filterKeys = Object.keys(filters);
    filterKeys.forEach(key => filters[key].forEach(val => params.append(key, val)));
    const paramStr = params.toString();
    const src = paramStr.length ? `finance/receipts/?${paramStr}` : "finance/receipts/";

    function deleteReceipt(srId: number, receiptId: number) {
        const cmd = new FinanceReceiptCommand("remove", srId, receiptId);
        getCommandRepository()
            .sendCommand(cmd)
            .then(() => {
                setDeleting(false);
                setForceReload(forceReload + 1);
            });
    }

    return (
        <Box m={1}>
            <Box my={1}>
                {props.serviceRecipientId && (
                    <Button
                        key="new-receipt"
                        variant="outlined"
                        onClick={() => {
                            setReceiptId(undefined);
                            setEditing(true);
                        }}
                    >
                        new receipt
                    </Button>
                )}
            </Box>
            <SchemaList
                title="receipts"
                src={src}
                displayFields={["receivedDate", "amount", "description"]}
                filterFields={[]}
                filters={filters}
                onFilter={filters => {
                    setPage(0);
                    setFilters(filters);
                }}
                page={page}
                onPage={setPage}
                onSearch={search => {
                    setPage(0);
                    setSearch(search);
                }}
                searchText={search}
                /*onRowClick={f => {
                    setReceiptId((f as FinanceReceiptDto).receiptId);
                    setEditing(true);
                }}*/
                actionRenderer={d => {
                    return (
                        <>
                            <IconButton
                                size="small"
                                onClick={() => {
                                    setReceiptId((d as FinanceReceiptDto).receiptId);
                                    setEditing(true);
                                }}
                            >
                                <EditIcon />
                            </IconButton>
                            <IconButton
                                size={"small"}
                                onClick={() => {
                                    setReceiptId((d as FinanceReceiptDto).receiptId);
                                    setDeleting(true);
                                }}
                            >
                                <DeleteIcon />
                            </IconButton>
                        </>
                    );
                }}
            />
            {editing && (
                <FinanceReceiptModal
                    receiptId={receiptId}
                    serviceRecipientId={props.serviceRecipientId}
                    setShow={setEditing}
                    afterSave={() => setForceReload(forceReload + 1)}
                />
            )}
            {/* can't be bothered loading the receipt just to get the srId, so just assume */}
            {props.serviceRecipientId && deleting && (
                <EccoV3Modal
                    title="delete receipt"
                    maxWidth={"xs"}
                    show={true}
                    onCancel={() => setDeleting(false)}
                    onSave={() => {
                        deleteReceipt(props.serviceRecipientId!, receiptId!);
                    }}
                    action="delete"
                    saveEnabled={true}
                >
                    <Grid container>
                        <Grid item xs={12}>
                            {"delete this item?"}
                        </Grid>
                    </Grid>
                </EccoV3Modal>
            )}
        </Box>
    );
};
