/**
 * Represents the options when going to a review
 */
import {ReviewChoicesDto} from "../evidence-dto";

/** The percentage complete of the current review
 * which is simply the tab-index saved on the review against the total tab number */
export function getPercentComplete(totalOutcomes: number, reviewPage: number): number {
    // copy of the Java implementation moved here
    if (totalOutcomes == 0) {
        return 100;
    }
    let complete = (reviewPage * 100) / totalOutcomes;
    return complete > 100 ? 100 : Math.floor(complete); // was ': complete.toFixed(0);' but that is a string
}

export class ReviewChoices {

    constructor(public dto: ReviewChoicesDto) {
    }

    getPercentComplete(totalOutcomes: number) {
        return getPercentComplete(totalOutcomes, this.dto.reviewPage);
    }
}