import * as dto from "ecco-dto/acl-dto";

import ResultTable = require("../../controls/ResultTable");

/** This class is responsible for converting some user-desired acl input into a table which is suitable for a post */
class AclsBulkImportDisplayConverter {

    // hold what we add to the table in raw form, so we can easily 'post' the collection
    private aclEntries: dto.AclEntryDto[] = [];

    constructor(private aclListTable: ResultTable) {
    }

    public appendAclEntryToDisplay(aclEntry: dto.AclEntryDto): void {
        this.aclEntries.push(aclEntry);
        this.draw(aclEntry);
    }

    public appendAclInputToDisplay(usernamesTxt: string, id: number, clazz: string): void {

        // don't bother if there are no usernames
        if (!usernamesTxt)
            return;

        var debug = true;
        var usernamesArr = usernamesTxt.split('\n');
        for (var x=0; x<usernamesArr.length; x++) {
            var username: string = usernamesArr[x];
            if (debug)
                console.log("line: "+x+": " +username);

            var aclEntry: dto.AclEntryDto = new AclEntry();
            aclEntry.clazz = clazz;
            aclEntry.secureObjectId = id;
            aclEntry.username = username;
            aclEntry.permissionMask = 1;

            console.debug("appending aclEntry: " + aclEntry.clazz + ": " + aclEntry.secureObjectId + ": " + aclEntry.username);
            this.aclEntries.push(aclEntry);
            this.draw(aclEntry);
        }
    }

    private draw(aclEntry: dto.AclEntryDto) {
        this.aclListTable.append([{'username': aclEntry.username, 'secureObjectId': aclEntry.secureObjectId, 'clazz': aclEntry.clazz}]);
    }

    public empty(): void {
        this.aclEntries = [];
    }

    public remove(index: number): void {
        this.aclEntries.splice(index, 1);
    }

    public rePopulate(): void {
        this.aclEntries.forEach((dto) => {
            this.draw(dto);
        });
    }

    public extractAcls(): dto.AclEntryDto[] {
        return this.aclEntries;
    }

}

class AclEntry implements dto.AclEntryDto {
    username: string;
    secureObjectId: number;
    clazz: string;
    permissionMask: number;
}

export = AclsBulkImportDisplayConverter;
