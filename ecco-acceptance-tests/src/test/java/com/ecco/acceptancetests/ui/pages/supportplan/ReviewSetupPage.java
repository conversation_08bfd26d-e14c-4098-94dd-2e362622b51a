package com.ecco.acceptancetests.ui.pages.supportplan;

import org.joda.time.LocalDate;
import org.openqa.selenium.WebDriver;

import com.ecco.acceptancetests.ui.pages.BasePageObject;

public class ReviewSetupPage extends BasePageObject {

    protected static final String URL = "/dynamic/secure/needsAssessmentReductionReview";

    private static final String BUTTON_TEXT_SAVE = "create";
    private static final String BUTTON_TEXT_CONTINUE = "continue";

    public ReviewSetupPage(WebDriver webDriver) {
        super(URL, webDriver);
    }

    public ReviewPage setup(LocalDate reviewDate) {
        setDatePickerField("custom review date", reviewDate);
        clickButtonByText(BUTTON_TEXT_SAVE);
        return new ReviewPage(getWebDriver());
    }

    public ReviewPage continueReview() {
        clickButtonByText(BUTTON_TEXT_CONTINUE);
        return new ReviewPage(getWebDriver());
    }

}
